<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>跃上教育学生信息管理系统</title>
    <!-- 添加Open Graph Meta标签 -->
    <meta property="og:title" content="跃上教育®学生信息管理">
    <meta property="og:description" content="学生管理与签到系统&#10;限全职老师内部使用&#10;商标版权 跃上教育®">
    <meta property="og:image" content="">
    <meta property="og:url" content="">
    <script>
        // 动态设置og:url和图片路径
        // Meta标签设置已移至主window.onload中
    </script>
    <!-- 添加微信特定的Meta标签 -->
    <meta itemprop="name" content="跃上教育®学生信息管理系统">
    <meta itemprop="description" content="学生管理与签到系统&#10;限全职老师内部使用&#10;商标版权 跃上教育®">
    <meta itemprop="image" content="">
    <link rel="icon" type="image/x-icon" href="./ys.ico">
    <link rel="shortcut icon" type="image/x-icon" href="./ys.ico">
    <link rel="apple-touch-icon" href="./ys.ico">
    <!-- 添加农历转换库 -->
    <script>
        // 创建全局的、统一的农历库加载器
        window.lunarLibraryLoader = new Promise((resolve, reject) => {
            // 如果库已存在，则直接完成
            if (typeof Lunar !== 'undefined') {
                console.log('农历库已加载。');
                return resolve();
            }

            const localPath = '/node_modules/lunar-javascript/lunar.js';
            const cdnPath = 'https://cdn.jsdelivr.net/npm/lunar-javascript/lunar.js';
            
            // 设置一个总的超时时间
            let timeoutHandle = setTimeout(() => {
                reject(new Error('农历库加载超时'));
            }, 5000);

            const onScriptLoad = (path) => {
                clearTimeout(timeoutHandle);
                // console.log(`农历库加载成功: ${path}`);
                resolve();
            };
            
            const tryCdn = () => {
                console.warn(`本地农历库加载失败，尝试从 CDN 加载...`);
                const cdnScript = document.createElement('script');
                cdnScript.src = cdnPath;
                cdnScript.onload = () => onScriptLoad(cdnPath);
                cdnScript.onerror = () => {
                    clearTimeout(timeoutHandle);
                    console.error(`CDN 农历库也加载失败，将使用公历日期。`);
                    reject(new Error('CDN and local failed'));
                };
                document.head.appendChild(cdnScript);
            };

            // console.log('尝试优先从本地加载农历库...');
            const localScript = document.createElement('script');
            localScript.src = localPath;
            localScript.onload = () => onScriptLoad(localPath);
            localScript.onerror = tryCdn;
            document.head.appendChild(localScript);
        });

        // 统一处理加载失败的情况
        window.lunarLibraryLoader.catch(err => {
            console.error('农历库加载流程最终失败:', err.message);
            // 触发失败事件，以便其他部分的代码可以响应
            document.dispatchEvent(new CustomEvent('lunarLoadFailed'));
        });
    </script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', monospace;
            background: #f5f5f5;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            padding-bottom: 60px;  /* 给底部添加足够的空间 */
        }

        /* 标题样式 */
        .header {
            text-align: center;
            padding: 20px 0;
            margin-bottom: 30px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .header h1 {
            color: #1976d2;
            font-size: 32px;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;  /* 减小整体间距 */
            position: relative;
            padding: 0 200px;
        }

        .header .logo {
            height: 55px;
            width: auto;
            margin-right: 4px;
            filter: drop-shadow(0 0 10px rgba(25, 118, 210, 0.3));
            animation: logoFloat 3s ease-in-out infinite;
            position: relative;
            z-index: 2;
        }

        /* Logo浮动动画 */
        @keyframes logoFloat {
            0%, 100% {
                transform: translateY(0);
                filter: drop-shadow(0 0 10px rgba(25, 118, 210, 0.3));
            }
            50% {
                transform: translateY(-5px);
                filter: drop-shadow(0 0 15px rgba(25, 118, 210, 0.5));
            }
        }

        /* Logo光晕效果 */
        .header .logo::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(25, 118, 210, 0.2) 0%, rgba(25, 118, 210, 0) 70%);
            animation: glowPulse 3s ease-in-out infinite;
            z-index: -1;
        }

        @keyframes glowPulse {
            0%, 100% {
                opacity: 0.5;
                transform: translate(-50%, -50%) scale(1);
            }
            50% {
                opacity: 0.8;
                transform: translate(-50%, -50%) scale(1.1);
            }
        }

        /* Logo悬停效果 */
        .header .logo:hover {
            animation: none;
            transform: scale(1.1);
            transition: transform 0.3s ease;
            filter: drop-shadow(0 0 15px rgba(25, 118, 210, 0.6));
        }

        .header .logo:hover::after {
            animation: none;
            opacity: 0.8;
            transform: translate(-50%, -50%) scale(1.2);
            transition: all 0.3s ease;
        }

        .title-text {
            display: flex;
            align-items: center;
            position: relative;
            white-space: nowrap;
            gap: 15px;  /* 增加"跃上教育"和"学生签到系统"之间的间距 */
        }

        .title-text .company-name {
            position: relative;
            margin-right: 0;
        }

        .registered-mark {
            font-size: 14px;
            position: absolute;
            top: -2px;  /* 调整商标标志的垂直位置 */
            right: -14px;
            color: #1976d2;
        }

        /* 添加登录信息样式 */
        .login-info {
            position: absolute;
            left: 20px;
            font-size: 14px;
            color: #666;
            text-align: left;
            line-height: 1.5;
            z-index: 1000;
        }

        .login-info .user {
            color: #1976d2;
            font-weight: bold;
        }

        .status-date {
            color: #666;
            font-size: 14px;
            margin-top: 5px;
            text-align: left;
            line-height: 1.4;
            display: flex;
            justify-content: space-between;
            width: 173px;  /* 设置固定宽度 */
        }

        .status-lunar-time {
            color: #666;
            font-size: 14px;
            margin-top: 2px;
            text-align: left;
            line-height: 1.4;
            display: flex;
            justify-content: space-between;
            width: 185px;  /* 设置相同的宽度以保持对齐 */
        }

        .status-time {
            color: #1565c0;  /* 更深的蓝色 */
            font-weight: bold;  /* 加粗 */
        }

        /* 修改退出登录按钮样式 */
        .logout-button {
            margin-left: 10px;
            padding: 2px 8px;
            background: #ff4444;
            color: white;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            vertical-align: baseline;
            position: relative;
            top: -1px;
            line-height: normal;
            height: 20px;
            z-index: 100;
        }

        .logout-button:hover {
            background: #ff1111;
        }

        .login-info div {
            display: flex;
            align-items: center;
            line-height: 20px;
            gap: 2px;
        }

        /* 修改头部按钮样式 */
        .header-buttons {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: row;
            gap: 10px;
            z-index: 10;
        }
        
        .buttons-column {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .header-button {
            padding: 6px 10px;
            width: 90px;
            height: 32px;
            border: none;
            border-radius: 4px;
            background: #1976d2;
            color: white;
            cursor: pointer;
            font-size: 13px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .header-button:hover {
            background: #1565c0;
            transform: translateX(-2px);
        }

        .header-button svg {
            width: 16px;
            height: 16px;
            flex-shrink: 0;  /* 防止图标缩小 */
        }

        /* 移除旧的时间显示样式 */
        .current-time {
            display: none;
        }

        /* 年级选择器样式 */
        .grade-tabs {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .grade-tab {
            padding: 8px 20px;
            border: none;
            border-radius: 5px;
            background: white;
            cursor: pointer;
            font-size: 14px;
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            position: relative; /* 为角标添加相对定位 */
        }

        /* 年级按钮"新"角标样式 */
        .grade-new-badge {
            position: absolute;
            top: -8px;
            left: -8px;
            background-color: #ff5722;
            color: white;
            font-size: 12px;
            font-weight: bold;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            line-height: 1;
            font-family: Arial, sans-serif;
            text-align: center;
        }

        .grade-tab:hover {
            background: #e3f2fd;
        }

        .grade-tab:active {
            background: #1976d2;
            color: white;
        }

        .grade-tab.active {
            background: #1976d2;
            color: white;
        }

        /* 添加显示模式切换按钮样式 */
        .display-mode-toggle {
            padding: 8px 20px;
            border: none;
            border-radius: 5px;
            background: white;
            cursor: pointer;
            font-size: 14px;
            margin-left: 10px;
            -webkit-tap-highlight-color: transparent;
            user-select: none;
        }

        .display-mode-toggle:hover {
            background: #e3f2fd;
        }

        .display-mode-toggle:active {
            background: #1976d2;
            color: white;
        }

        .display-mode-toggle.active {
            background: #1976d2;
            color: white;
        }

        /* 添加学校年级班级信息样式 */
        .school-info {
            position: absolute;
            bottom: 5px;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 14px;  /* 增大字体 */
            color: #333;      /* 加深颜色 */
            display: none;
            padding: 0 5px;   /* 添加左右内边距 */
        }

        .show-school-info .school-info {
            display: block;
        }

        .show-school-info .time-info {
            display: none;
        }

        /* 普通年级的学生卡片容器 */
        .students-container {
            display: none;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            padding: 20px;
        }

        .students-container.active {
            display: grid;
        }
        
        /* 教师年级的容器特殊处理 - 使用flex布局 */
        #grade-teacher.students-container {
            padding: 0 !important; /* 覆盖基础容器的padding */
        }

        #grade-teacher.students-container.active {
            display: block !important; /* 仅设置为块级元素，内部使用flex布局 */
        }

        /* 未签到统计容器的特殊样式 */
        #grade-unsign.students-container {
            display: none;
            padding: 0;
        }

        #grade-unsign.students-container.active {
            display: block;
        }

        /* 基础卡片样式 */
        .student-card {
            background: white;
            border-radius: 10px;
            padding: 10px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            position: relative;
            overflow: hidden;
            touch-action: manipulation;
            -webkit-tap-highlight-color: transparent;
        }

        /* 已签到学生卡片样式 */
        .signed {
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            box-shadow: 0 4px 12px rgba(102, 187, 106, 0.2);
        }

        /* 已签退状态 */
        .checked-out {
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            box-shadow: 0 4px 12px rgba(102, 187, 106, 0.2);
        }

        .student-card:hover {
            transform: translateY(-5px);
        }

        /* 修改名字样式 */
        .student-name {
            font-size: 18px;
            color: #333;
            margin-top: 4px;
            margin-bottom: 13px;
            user-select: text;
            cursor: text;
            position: relative;
            z-index: 3;
            display: inline-block;
            padding: 2px 4px;
            min-width: min-content;
            max-width: 100%;
            white-space: nowrap;
            pointer-events: auto;
            padding-left: 4px;
        }

        /* 基础头像样式 */
        .student-avatar {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            margin: 0 auto 10px;
            overflow: hidden;
            cursor: pointer;
            position: relative;
            z-index: 1;  /* 改为1，作为最底层 */
            transition: transform 0.3s ease;
            -webkit-tap-highlight-color: transparent;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .student-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
        }

        /* 修改信息按钮样式 */
        .info-button {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: none;
            background: rgba(25, 118, 210, 0.9);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            opacity: 0;
            transform: translateY(-10px);
            z-index: 3;  /* 改为3，作为最上层 */
        }

        /* 选中状态的卡片样式 */
        .student-card.selected {
            transform: scale(1.05);
            z-index: 2;
            position: relative;
            border: 2px solid #1976d2;
            background: linear-gradient(135deg, #e3f2fd 0%, #90caf9 100%) !important;
            box-shadow: 0 0 20px rgba(25, 118, 210, 0.4);
            overflow: hidden;
        }

        /* 扫光效果 */
        .student-card.selected::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(
                45deg,
                transparent 0%,
                rgba(255, 255, 255, 0.1) 45%,
                rgba(255, 255, 255, 0.5) 50%,
                rgba(255, 255, 255, 0.1) 55%,
                transparent 100%
            );
            transform: rotate(45deg);
            animation: sweep 2s linear infinite;
        }

        /* 扫光动画 */
        @keyframes sweep {
            0% {
                transform: translate(-100%, -100%) rotate(45deg);
            }
            100% {
                transform: translate(100%, 100%) rotate(45deg);
            }
        }

        /* 选中状态下显示积分和信息按钮 */
        .student-card.selected .student-points,
        .student-card.selected .info-button {
            opacity: 1;
            transform: translateY(0);
        }

        /* 选中状态下的头像效果 */
        .student-card.selected .student-avatar {
            transform: scale(1.05);
        }

        /* 移除旧的悬停效果 */
        .student-card:hover .info-button {
            opacity: 0;
            transform: translateY(-10px);
        }

        /* 移动端触摸反馈 */
        @media (hover: none) {
            .info-button {
                opacity: 0;
                transform: translateY(-10px);
            }
            
            .student-points {
                opacity: 0;
                transform: translateY(-10px);
            }
        }

        /* 添加波纹效果 */
        .student-card::after {
            display: none;
        }

        .student-card:active::after {
            display: none;
        }

        /* 修改学分显示样式 */
        .student-points {
            position: absolute;
            top: 8px;
            left: 8px;
            background: rgba(25, 118, 210, 0.9);
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: bold;
            z-index: 2;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        /* 添加伪元素扩展积分区域的可点击范围 */
        .student-points::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
            height: 25px; /* 向下延伸的区域大小 */
            z-index: 1;
        }

        /* 修改签到签退时间样式 */
        .time-info {
            position: absolute;
            bottom: 5px;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-between;
            padding: 0 10px;
            font-size: 12px;
            color: #666;
        }

        .check-in-time {
            position: absolute;
            bottom: 5px;
            left: 13px;
            background: #2e7d32;
            color: #ffffff;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 10px;
            line-height: 1.2;
            white-space: nowrap;
            z-index: 2;
        }

        .check-out-time {
            position: absolute;
            bottom: 5px;
            right: 13px;
            background: #ff4444;
            color: #ffffff;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 10px;
            line-height: 1.2;
            white-space: nowrap;
            z-index: 2;
        }

        /* 统一的媒体查询 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .grade-tab {
                padding: 6px 15px;
                font-size: 13px;
            }

            .students-container {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
                gap: 15px;
                padding: 10px;
            }

            .student-avatar {
                width: 120px;
                height: 120px;
            }

            .student-name {
                font-size: 16px;
                padding: 2px 6px;
            }

            .unsign-students {
                gap: 10px;
                padding: 5px;
            }

            .unsign-students .student-card {
                width: 100px;
                flex: 0 0 100px;
                padding: 8px;
            }

            .unsign-students .student-avatar {
                width: 60px;
                height: 60px;
            }

            .grade-section {
                margin: 10px 0;
                padding: 10px;
            }

            .modal-content {
                margin: 5% auto;
                width: 95%;
                padding: 12px;
            }

            .student-detail-avatar {
                width: 80px;
                height: 80px;
                margin-bottom: 12px;
            }

            .student-info p {
            font-size: 13px;
                padding: 6px;
                margin: 6px 0;
            }

            .student-info strong {
                min-width: 45px;  /* 从60px改为45px */
            }
        }

        @media (max-width: 480px) {
        .student-name {
                font-size: 15px;
                padding: 2px 4px;
            }

            .student-avatar {
                width: 100px;
                height: 100px;
            }

            .unsign-students .student-card {
                width: 90px;
                flex: 0 0 90px;
            }

            .unsign-students .student-avatar {
                width: 50px;
                height: 50px;
            }
        }

        /* 修改模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        /* 学生信息模态框样式 */
        #studentModal {
            z-index: 1100;  /* 确保显示在其他模态框之上 */
        }

        /* 同班同学搜索结果模态框样式 */
        #classmatesModal {
            z-index: 1200;  /* 确保显示在学生信息模态框之上 */
        }

        /* 模态框样式 */
        .modal-content {
            background: white;
            border-radius: 8px;
            padding: 20px;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 400px;
            max-height: 80vh;
            overflow-y: auto;
            overflow-x: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        /* 签到详情模态框样式 */
        .time-details-modal {
            width: 520px !important;
        }

        /* 教师考勤详情模态框样式 */
        .teacher-time-details-modal {
            width: 625px !important;
        }

        /* 添加表头固定样式 */
        .time-details-header {
            display: grid;
            grid-template-columns: 90px 60px 100px 100px 100px;
            padding: 10px;  /* 统一内边距 */
            background: #e3f2fd;
            font-weight: bold;
            color: #1976d2;
            border-radius: 5px 5px 0 0;
            margin: 0;
            position: sticky;
            top: 0;
            z-index: 1;
            text-align: center;
            width: 100%;
            box-sizing: border-box;
            gap: 0;
        }

        #timeDetailsList {
            margin: 10px 0 0 0;  /* 只保留上边距 */
            padding: 0;
            width: 100%;
            box-sizing: border-box;
        }

        /* 修改关闭按钮样式，确保始终可点击 */
        .close-modal {
            position: sticky;
            top: 0px;
            right: 10px;
            float: right;
            font-size: 24px;
            color: #666;
            cursor: pointer;
            transition: color 0.3s ease;
            z-index: 1000;
            background: #fff;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            margin-bottom: -30px;
            margin-top: -10px;
            line-height: 1;  /* 添加行高控制 */
            padding-bottom: 2px;  /* 微调垂直位置 */
        }

        .close-modal:hover {
            color: #ff4444;
        }

        #timeDetailsTitle {
            margin: 0 0 20px 0;
            color: #1976d2;
            font-size: 18px;
            text-align: center;
            padding: 0 30px;
            clear: both;
        }

        #timeDetailsList {
            font-size: 14px;
            line-height: 1.6;
            margin-top: 10px;
        }

        /* 添加滚动条样式 */
        .modal-content::-webkit-scrollbar {
            width: 8px;
            background: transparent;
        }

        .modal-content::-webkit-scrollbar-track {
            background: transparent;
            border-radius: 4px;
        }

        .modal-content::-webkit-scrollbar-thumb {
            background: transparent;
            border-radius: 4px;
            transition: background 0.3s ease;
        }

        .modal-content:hover::-webkit-scrollbar-thumb {
            background: #888;
        }

        .modal-content:hover::-webkit-scrollbar-thumb:hover {
            background: #666;
        }

        /* 确保内容区域不会因为滚动条消失而抖动 */
        .modal-content {
            scrollbar-gutter: stable;
        }

        /* 修改学生统计详情页的网格布局，增加积分列 */
        .time-details-modal .time-details-header,
        .time-details-modal .time-details-item {
            grid-template-columns: 90px 60px 100px 100px 100px !important;
            gap: 0 !important;
        }

        /* 进一步优化时间列的显示 */
        .time-details-signin, 
        .time-details-signout, 
        .time-details-duration {
            padding: 0 !important;
            margin: 0 !important;
        }

        /* 调整时间框的宽度 */
        .time-box {
            min-width: 60px !important;
            padding: 2px 3px !important;
            margin: 0 !important;
        }

        /* 积分列样式 */
        .time-details-points {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .time-details-points .points-box {
            background-color: #fff9c4;
            color: #f57f17;
            border-radius: 4px;
            padding: 2px 6px;
            font-weight: bold;
            font-size: 13px;
            display: inline-block;
        }

        .student-detail {
            text-align: center;
            padding: 10px 0;
        }

        .student-detail-avatar {
            width: 100px;
            height: 100px;
            margin: 0 auto 15px;
            border-radius: 50%;
            overflow: hidden;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }

        .student-detail-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .student-detail h2 {
            color: #1976d2;
            margin-bottom: 15px;
            font-size: 20px;
        }

        .student-info {
            text-align: left;
            padding: 0 15px;
        }

        .student-info p {
            margin: 8px 0;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 6px;
            font-size: 14px;
            line-height: 1.4;
            display: flex;
            align-items: center;
        }

        .student-info p span {
            flex: 1;
        }

        .student-info strong {
            color: #1976d2;
            margin-right: 2px;
            display: inline-block;
            min-width: 50px;  /* 从70px改为50px */
        }

        /* 添加图标样式 */
        .icon-link {
            display: inline-flex;
            align-items: center;
            color: #1976d2;
            text-decoration: none;
            cursor: pointer;
            margin-left: 5px;
        }

        .icon-link:hover {
            color: #1565c0;
        }

        .icon-link svg {
            width: 18px;
            height: 18px;
            margin-left: 5px;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { transform: translateY(-20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        /* 电话号码相关样式 */
        .phone-number {
            display: flex;
            align-items: center;
            margin: 4px 0;
            gap: 2px;
            margin-left: -12px;  /* 整体向左移动 */
        }

        .phone-number input {
            width: 150px;
            flex: none !important;
            padding: 4px 8px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            font-size: 14px;
            color: #333;
            outline: none;
            background: white;
            margin-right: 0;
        }

        /* 电话操作按钮基础样式 */
        .phone-action-btn {
            width: 22px;
            height: 22px;
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            padding: 0;
            font-size: 16px;
            line-height: 12px;  /* 调整符号垂直位置 */
            color: white;
            transition: all 0.3s ease;
        }

        .phone-action-btn:hover {
            transform: scale(1.1);
        }

        /* 删除和添加按钮的特定样式 */
        .phone-delete-btn {
            background: #ff4444;
            transform: scale(0.95);  /* 稍微缩小一点 */
            font-size: 20px;  /* 增加减号的字体大小 */
            line-height: 1px;  /* 减小行高使减号视觉上居中 */
            padding-bottom: 4px;  /* 增加底部内边距来调整位置 */
        }

        .phone-add-btn {
            background: #4caf50;
            line-height: 1px;  /* 减小行高使加号视觉上居中 */
            padding-bottom: 1px;  /* 增加底部内边距来调整位置 */
        }

        /* 电话图标样式 */
        .phone-number .icon-link {
            flex: none;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 2px;
        }

        .phone-number .icon-link svg {
            width: 22px;
            height: 22px;
        }

        /* 电话操作按钮容器 */
        .phone-actions {
            display: flex;
            gap: 5px;
            margin-left: 5px;
        }

        /* 电话容器布局 */
        #phoneContainer {
            display: flex;
            align-items: center;
            gap: 5px;
            margin: 8px 0;
            background: #f8f9fa;
            border-radius: 6px;
            padding: 8px;
        }

        #phoneContainer > p {
            display: flex;
            align-items: center;
            margin: 0;
            padding: 0;
            background: none;
            border-radius: 0;
            width: auto;
        }

        #phoneList {
            display: flex;
            flex-direction: column;
            gap: 4px;
            margin: 0;
        }

        /* 未签到标签样式 */
        .unsign-tab {
            background: white;
            color: inherit;
        }

        /* 未签退标签样式 */
        .unsignout-tab {
            background: white;
            color: inherit;
        }

        /* 未签到和未签退按钮的悬停和激活状态 */
        .unsign-tab:hover,
        .unsignout-tab:hover {
            background: #e3f2fd;
        }

        .unsign-tab.active,
        .unsignout-tab.active {
            background: #1976d2;
            color: white;
        }

        /* 未签到汇总区域样式 */
        .unsign-summary {
            width: 100%;
            padding: 20px;
        }

        .grade-section {
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            width: 100%;
            box-sizing: border-box;
        }

        .grade-section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 15px;
            background: #f5f5f5;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .grade-section-left {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .grade-section-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        .grade-section-count {
            color: #666;
            font-size: 14px;
        }

        /* 批量签到/签退按钮基础样式 */
        .batch-sign-button {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            color: white;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        /* 签到按钮样式(绿色) */
        .batch-sign-button[data-grade]:not([data-signout="true"]) {
            background: #2e7d32;  /* 使用与签到时间相同的绿色 */
        }

        .batch-sign-button[data-grade]:not([data-signout="true"]):hover {
            background: #1b5e20;
        }

        .batch-sign-button[data-grade]:not([data-signout="true"]):active {
            background: #1b4d1b;
        }

        /* 签退按钮样式(红色) */
        .batch-sign-button[data-signout="true"] {
            background: #d32f2f;  /* 使用与签退时间相同的红色 */
        }

        .batch-sign-button[data-signout="true"]:hover {
            background: #c62828;
        }

        .batch-sign-button[data-signout="true"]:active {
            background: #b71c1c;
        }

        .batch-sign-button:disabled {
            background: #ccc !important;
            cursor: not-allowed;
        }

        .unsign-students {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            padding: 10px;
            width: 100%;
            box-sizing: border-box;
            justify-content: flex-start;
            align-items: flex-start;
        }

        /* 未签到学生卡片特殊样式 */
        .unsign-students .student-card {
            width: 120px;
            flex: 0 0 120px;
            margin: 0;
            background: #fff;
            border-radius: 10px;
            padding: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            align-items: center;
            box-sizing: border-box;
            height: auto;
            position: relative;  /* 确保相对定位 */
        }

        /* 未签到学生卡片的信息按钮样式 */
        .unsign-students .student-card .info-button {
            position: absolute;
            right: 3px;
            top: 3px;
            width: 24px;
            height: 24px;
            opacity: 0;
            transform: translateY(-10px);
            background: rgba(25, 118, 210, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 2;
            transition: opacity 0.3s ease, transform 0.3s ease;
        }

        .unsign-students .student-card .info-button svg {
            width: 16px;
            height: 16px;
        }

        /* 未签到汇总界面选中状态下显示信息按钮 */
        .unsign-students .student-card.selected .info-button {
            opacity: 1;
            transform: translateY(0);
        }

        /* 未签退汇总界面的信息按钮样式 */
        #grade-unsignout .student-card .info-button {
            position: absolute;
            right: 3px;
            top: 3px;
            width: 24px;
            height: 24px;
            opacity: 0;
            transform: translateY(-10px);
            background: rgba(25, 118, 210, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 2;
            transition: opacity 0.3s ease, transform 0.3s ease;
        }

        #grade-unsignout .student-card .info-button svg {
            width: 16px;
            height: 16px;
        }

        /* 未签退汇总界面选中状态下显示信息按钮 */
        #grade-unsignout .student-card.selected .info-button {
            opacity: 1;
            transform: translateY(0);
        }

        .unsign-students .student-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            overflow: hidden;
            margin-bottom: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);  /* 添加阴影效果 */
        }

        .unsign-students .student-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* 未签到学生卡片的名字样式 */
        .unsign-students .student-name {
            font-size: 14px;
            font-weight: 500;  /* 加粗字体 */
            color: #333;
            margin-top: 4px;  /* 保持与通用样式一致 */
            margin-bottom: 10px;
            text-align: center;
            width: auto;
            display: inline-block;
            padding: 2px 4px;
            min-width: min-content;
            max-width: 100%;
            white-space: nowrap;
        }

        /* 修改请假标记样式 */
        .leave-badge {
            position: absolute;
            top: 12px;
            right: 9px;  /* 改为右对齐 */
            background: #ff5722;
            color: #ffffff;
            height: 22px;
            line-height: 22px;
            padding: 0 8px;
            border-radius: 11px;
            font-size: 12px;
            font-weight: 500;
            z-index: 2;
            text-align: center;
            pointer-events: none;
            box-shadow: 0 2px 4px rgba(255, 87, 34, 0.3);
        }

        /* 添加工号标签样式 */
        .workid-badge {
            position: absolute;
            top: 5px;
            left: 5px;
            background: rgba(25, 118, 210, 0.9);
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: bold;
            z-index: 2;
            text-align: center;
            box-shadow: 0 2px 4px rgba(25, 118, 210, 0.3);
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        /* 添加伪元素扩展可点击区域 */
        .workid-badge::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
            height: 25px; /* 向下延伸的区域大小 */
            z-index: 1;
        }
        
        /* 选中状态下显示工号标签 */
        .student-card.selected .workid-badge {
            opacity: 1;
            transform: translateY(0);
        }

        /* 未签到和未签退汇总界面的请假标签保持在左上角 */
        #grade-unsign .leave-badge,
        #grade-unsignout .leave-badge {
            top: 2px !important;
            left: 1px !important;  /* 确保在左侧 */
            right: auto !important;  /* 取消右对齐 */
        }

        .student-card.on-leave {
            background: #fff3e0;
        }

        /* 选中状态下的姓名样式 */
        .selected .student-name {
            color: #0d47a1;
            font-weight: 900;
            text-shadow: 0 0 4px rgba(255, 255, 255, 0.5);
        }

        /* 修改状态栏样式 */
        .status-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            padding: 10px 10px;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: flex-start;  /* 改为靠左对齐 */
            align-items: center;
            z-index: 1000;
            font-size: 16px;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 15px;
            justify-content: flex-start;  /* 改为靠左对齐 */
            flex: 1;
            text-align: left;  /* 改为靠左对齐 */
        }

        .status-center {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            background: #1976d2;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-weight: bold;
            opacity: 0;
            transition: opacity 0.3s ease;
            text-align: center;  /* 文字居中 */
        }

        .status-right {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            align-items: center;
        }

        .status-total,
        .status-signed,
        .status-unsigned,
        .status-leave,
        .status-grade {
            text-align: left;  /* 改为靠左对齐 */
            white-space: nowrap;  /* 防止文字换行 */
        }

        .status-total {
            color: #333;
        }

        .status-signed {
            color: #2e7d32;
        }

        .status-unsigned {
            color: #c62828;
        }

        .status-leave {
            color: #ff9800;
        }

        .status-date {
            color: #666;
        }

        /* 未签到汇总区域的学校信息特殊样式 */
        .unsign-students .school-info {
            font-size: 12px;  /* 更小的字体 */
            white-space: nowrap;  /* 防止换行 */
            overflow: hidden;
            text-overflow: ellipsis;  /* 超出显示省略号 */
            padding: 0 3px;  /* 减小内边距 */
        }

        /* 未签退标签样式 */
        .unsignout-tab {
            background: white;
            color: inherit;
        }

        /* 未签退汇总区域样式 */
        #grade-unsignout.students-container {
            display: block;
            padding: 0;
        }

        #grade-unsignout.students-container.active {
            display: block;
        }

        #grade-unsignout .unsign-summary {
            width: 100%;
            padding: 20px;
        }

        #grade-unsignout .grade-section {
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            width: 100%;
            box-sizing: border-box;
        }

        #grade-unsignout .unsign-students {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            padding: 10px;
            width: 100%;
            box-sizing: border-box;
            justify-content: flex-start;
            align-items: flex-start;
        }

        #grade-unsignout .student-card {
            width: 120px;
            flex: 0 0 120px;
            margin: 0;
            background: #fff;
            border-radius: 10px;
            padding: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            align-items: center;
            box-sizing: border-box;
            height: auto;
            position: relative;
        }

        /* 移除签到状态圆点样式 */
        .sign-status {
            display: none;
        }

        /* 修改已签到卡片样式 */
        .student-card.signed {
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            box-shadow: 0 4px 12px rgba(102, 187, 106, 0.2);
        }

        /* 修改时间信息样式 */
        .time-info {
            position: absolute;
            bottom: 5px;
            left: 5px;  /* 改为左对齐 */
            text-align: left;
            display: flex;
            gap: 5px;
        }

        /* 调整签到和签退时间的样式 */
        .check-in-time {
            background: #2e7d32;
            color: #ffffff;
            padding: 2px 6px;
            border-radius: 12px;
            font-size: 12px;
            display: none;  /* 默认隐藏 */
        }

        .check-out-time {
            background: #ff4444;
            color: #ffffff;
            padding: 2px 6px;
            border-radius: 12px;
            font-size: 12px;
            display: none;  /* 默认隐藏 */
        }

        /* 未签退统计中的学生卡片样式 */
        #grade-unsignout .student-card {
            background: white;
            border-radius: 10px;
            padding: 10px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        #grade-unsignout .student-card.signed {
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            box-shadow: 0 4px 12px rgba(102, 187, 106, 0.2);
        }

        .status-left {
            cursor: pointer;
        }
        
        .time-details-item {
            display: grid;
            grid-template-columns: 90px 60px 100px 100px 100px;
            padding: 10px;  /* 统一内边距 */
            border-bottom: 1px solid #eee;
            align-items: center;
            text-align: center;
            width: 100%;
            box-sizing: border-box;
            gap: 0;
        }
        
        .time-details-header {
            display: grid;
            grid-template-columns: 90px 60px 100px 100px 100px;
            padding: 10px;  /* 统一内边距 */
            background: #e3f2fd;
            font-weight: bold;
            color: #1976d2;
            border-radius: 5px 5px 0 0;
            margin: 0;
            position: sticky;
            top: 0;
            z-index: 1;
            text-align: center;
            width: 100%;
            box-sizing: border-box;
            gap: 0;
        }

        .time-details-name {
            text-align: center;  /* 居中对齐 */
        }

        .time-details-duration .time-box {
            min-width: 65px;  /* 为在校时长增加宽度 */
        }

        /* 修改学生卡片上的时间显示样式 */
        .time-info {
            position: absolute;
            bottom: 5px;
            left: 5px;
            right: 5px;
            display: flex;
            gap: 5px;
            justify-content: center;
            min-height: 20px;  /* 保持一致的高度 */
        }

        .check-in-time, .check-out-time {
            padding: 2px 4px;
            border-radius: 12px;
            font-size: 12px;
            color: #fff;
            display: none;  /* 默认隐藏 */
        }

        .check-in-time:not(:empty) {
            display: inline-block;  /* 有内容时才显示 */
            background: #2e7d32;
        }

        .check-out-time:not(:empty) {
            display: inline-block;  /* 有内容时才显示 */
            background: #ff4444;
        }

        .lunar-date {
            color: #666;
            font-size: 14px;
            margin-right: 5px;
            white-space: nowrap;
        }

        .time-box {
            display: inline-block;
            padding: 2px 4px;
            border-radius: 4px;
            min-width: 65px;  /* 减小最小宽度 */
            text-align: center;
            box-sizing: border-box;
        }

        .time-details-signin, .time-details-signout, .time-details-duration {
            text-align: center;
            padding: 0 2px;  /* 减小内边距 */
            box-sizing: border-box;  /* 包含内边距在内的盒模型 */
        }

        #timeDetailsList {
            margin: 10px 0 0 0;  /* 只保留上边距 */
            padding: 0;
            width: 100%;
            box-sizing: border-box;
        }

        /* 添加排序箭头样式 */
        .time-details-header span {
            text-align: center;
            user-select: none;
            position: relative;
            padding-right: 15px;  /* 为箭头留出空间 */
        }

        .time-details-header span:first-child {
            padding-right: 0;  /* 第一列不需要箭头空间 */
        }

        .time-details-header span > span::after,
        .time-details-header span:not(:first-child)::after {
            content: '';
            position: absolute;
            right: 2px;
            top: 50%;
            width: 0;
            height: 0;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            opacity: 0.3;
            border-top: 4px solid currentColor;
            transform: translateY(-50%);
        }

        .time-details-header span > span.sort-asc::after,
        .time-details-header span:not(:first-child).sort-asc::after {
            border-top: 0;
            border-bottom: 4px solid currentColor;
            opacity: 1;
        }

        .time-details-header span > span.sort-desc::after,
        .time-details-header span:not(:first-child).sort-desc::after {
            border-bottom: 0;
            border-top: 4px solid currentColor;
            opacity: 1;
        }

        .time-details-name {
            text-align: center;
        }

        /* 修改表头和内容的样式 */
        .time-details-item,
        .time-details-header {
            display: grid;
            grid-template-columns: 90px 60px 100px 100px 100px;
            padding: 10px;
            width: 100%;
            box-sizing: border-box;
            gap: 0;
            text-align: center;
            align-items: center;
        }
        
        /* 添加教师考勤详情的特殊样式 */
        .time-details-header.teacher-header,
        .time-details-item.teacher-item {
            grid-template-columns: 80px 60px 80px 105px 105px 110px;
        }
        
        .time-details-workid,
        .time-details-subject {
            text-align: center;
        }

        .time-details-item {
            border-bottom: 1px solid #eee;
        }
        
        .time-details-header {
            background: #e3f2fd;
            font-weight: bold;
            color: #1976d2;
            border-radius: 5px 5px 0 0;
            margin: 0;
            position: sticky;
            top: 0;
            z-index: 1;
        }

        /* 添加排序箭头样式 */
        .time-details-header span {
            text-align: center;
            user-select: none;
            position: relative;
            padding-right: 15px;  /* 为箭头留出空间 */
        }

        .time-details-header span:first-child {
            padding-right: 0;  /* 第一列不需要箭头空间 */
        }

        .time-details-header span > span {
            padding-right: 15px;  /* 姓名文字后面的箭头空间 */
        }

        .time-details-header span > span::after,
        .time-details-header span:not(:first-child)::after {
            content: '';
            position: absolute;
            right: 2px;
            top: 50%;
            width: 0;
            height: 0;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            opacity: 0.3;
            border-top: 4px solid currentColor;
            transform: translateY(-50%);
        }

        .time-details-header span > span.sort-asc::after,
        .time-details-header span:not(:first-child).sort-asc::after {
            border-top: 0;
            border-bottom: 4px solid currentColor;
            opacity: 1;
        }

        .time-details-header span > span.sort-desc::after,
        .time-details-header span:not(:first-child).sort-desc::after {
            border-bottom: 0;
            border-top: 4px solid currentColor;
            opacity: 1;
        }

        .time-details-name {
            text-align: center;
        }

        /* 添加下拉框样式 */
        .student-info select {
            flex: 1;
            padding: 4px 8px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            background: white;
            font-size: 14px;
            color: #333;
            outline: none;
        }

        .student-info select:focus {
            border-color: #1976d2;
        }

        /* 添加输入框样式 */
        .student-info input {
            flex: 1;
            padding: 4px 8px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            font-size: 14px;
            color: #333;
            outline: none;
            background: white;
        }

        .student-info input:focus {
            border-color: #1976d2;
        }

        /* 添加备注文本框样式 */
        .student-info textarea {
            flex: 1;
            padding: 4px 8px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            font-size: 14px;
            color: #333;
            outline: none;
            background: white;
            resize: vertical;
            min-height: 50px;
            width: 100%;
            box-sizing: border-box;
        }

        .student-info textarea:focus {
            border-color: #1976d2;
        }

        /* 搜索模态框样式 */
        .search-modal-content {
            width: 300px;
            padding: 20px;
            position: fixed;
            top: 35%;
            left: 50%;
            transform: translate(-50%, -50%);
            margin: 0;
        }

        /* 同班同学搜索结果模态框样式 */
        .classmates-tabs {
            display: flex;
            border-bottom: 2px solid #e0e0e0;
            margin-bottom: 15px;
        }

        .classmates-tab {
            flex: 1;
            padding: 10px;
            border: none;
            background: #f5f5f5;
            color: #666;
            cursor: pointer;
            border-radius: 4px 4px 0 0;
            margin-right: 2px;
            transition: all 0.3s ease;
        }

        .classmates-tab:last-child {
            margin-right: 0;
        }

        .classmates-tab.active {
            background: #1976d2;
            color: white;
        }

        .classmates-tab:hover:not(.active) {
            background: #e0e0e0;
        }

        .classmates-tab-content {
            display: none;
        }

        .classmates-tab-content.active {
            display: block;
        }

        .classmates-list {
            max-height: 400px;
            overflow-y: auto;
            padding-bottom: 10px;
        }

        .classmate-item {
            display: flex;
            align-items: flex-start;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin-bottom: 8px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
            min-height: 70px;
            position: relative;
        }

        .classmate-item:hover {
            background: #f5f5f5;
            border-color: #1976d2;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .classmate-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin-right: 15px;
            object-fit: cover;
            border: 2px solid #e0e0e0;
            flex-shrink: 0;
        }

        .classmate-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            min-width: 0; /* 允许文本截断 */
        }

        .classmate-name {
            font-weight: bold;
            font-size: 18px;
            color: #333;
            margin-bottom: 6px;
            line-height: 1.2;
        }

        .classmate-school-info {
            font-size: 14px;
            color: #555;
            margin-bottom: 4px;
            line-height: 1.3;
            font-weight: 500;
        }

        .classmate-address {
            font-size: 13px;
            color: #777;
            line-height: 1.3;
        }

        .classmate-notes {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 12px;
            color: #999;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 12px;
            border: 1px solid #e9ecef;
            max-width: 120px;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .classmate-notes:empty {
            display: none;
        }

        .classmate-notes.graduated {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }

        .classmate-notes.active {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .classmate-notes.transferred {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .classmate-item {
                padding: 12px;
            }

            .classmate-avatar {
                width: 45px;
                height: 45px;
                margin-right: 12px;
            }

            .classmate-name {
                font-size: 16px;
            }

            .classmate-school-info {
                font-size: 13px;
            }

            .classmate-address {
                font-size: 12px;
            }

            .classmate-notes {
                position: static;
                transform: none;
                margin-top: 4px;
                max-width: none;
                font-size: 11px;
            }
        }

        .empty-result {
            text-align: center;
            padding: 40px 20px;
            color: #999;
            font-size: 14px;
        }

        .empty-result svg {
            width: 48px;
            height: 48px;
            margin-bottom: 12px;
            opacity: 0.5;
        }

        .search-modal-content .close-modal {
            position: absolute;
            right: 10px;
            top: 20px;
            font-size: 24px;
            cursor: pointer;
            color: #1976d2;
            transition: color 0.3s ease;
            width: 24px;
            height: 24px;
            line-height: 24px;
            text-align: center;
        }

        @media (max-width: 768px) {
            .search-modal-content {
                width: 90% !important;
                max-width: 300px;
            }
        }

        .search-input-container {
            position: relative;
            margin: 20px 0;
        }

        .search-input {
            width: 100%;
            padding: 8px 12px;
            border: 2px solid #e0e0e0;
            border-radius: 4px;
            font-size: 16px;
            outline: none;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            border-color: #1976d2;
        }

        .search-results {
            max-height: 300px;
            overflow-y: auto;
        }

        .search-result-item {
            padding: 10px;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            transition: all 0.2s ease;
        }

        .search-result-item:hover {
            background: #f5f5f5;
        }

        .search-result-item img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 12px;
            object-fit: cover; /* 添加这一行，确保图片填充空间并保持比例 */
        }

        .search-result-info {
            flex: 1;
        }

        .search-result-name {
            font-weight: bold;
            margin-bottom: 4px;
        }

        .search-result-pinyin {
            font-size: 12px;
            color: #666;
        }

        /* 搜索按钮样式 */
        .search-button {
            padding: 8px 20px;
            border: none;
            border-radius: 5px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 6px;
            margin-left: 10px;
        }

        .search-button:hover {
            background: #e3f2fd;
        }

        .search-button.active {
            background: #1976d2;
            color: white;
        }

        .search-button svg {
            width: 16px;
            height: 16px;
            fill: currentColor;
        }

        .time-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f5f5f5;
            border: 1px solid #ddd;
            margin: 0 5px;
        }

        .time-status:hover {
            background: #e0e0e0;
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .time-status.signed {
            background: #4CAF50;
            color: white;
            border-color: #4CAF50;
        }

        .time-status.signed-out {
            background: #ff4444;
            color: white;
            border-color: #ff4444;
        }

        .time-status.not-signed {
            background: #ff4444;
            color: white;
            border-color: #ff4444;
        }

        /* 添加动画效果 */
        @keyframes statusPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .time-status:active {
            animation: statusPulse 0.3s ease;
        }

        /* 消息提示样式 */
        .message-container {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10000;
            display: flex;
            flex-direction: column;
            gap: 10px;
            pointer-events: none;
            width: 80%;  /* 限制宽度 */
            max-width: 300px;  /* 最大宽度 */
        }
        
        .message {
            padding: 10px 20px;
            border-radius: 4px;
            background-color: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            animation: slideIn 0.3s ease-out;
            font-size: 14px;
            opacity: 1;
            transition: opacity 0.3s ease-out;
            text-align: center;
            white-space: normal;  /* 允许文字换行 */
            word-wrap: break-word;  /* 长单词换行 */
        }
        
        .message.success {
            background-color: #4caf50;
            color: white;
        }
        
        .message.error {
            background-color: #f44336;
            color: white;
        }
        
        .message.info {
            background-color: #2196f3;
            color: white;
        }
        
        .message.fade-out {
            opacity: 0;
        }
        
        @keyframes slideIn {
            from {
                transform: translateY(-20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* 教师卡片布局样式 */
        #grade-teacher .teacher-cards {
            display: flex !important;
            flex-wrap: wrap !important;
            flex-direction: row !important;
            justify-content: flex-start !important;
            gap: 15px !important;
            padding: 0px 20px 20px 20px !important;
            width: 100% !important;
            box-sizing: border-box !important;
            /* 重要：避免grid布局的影响 */
            grid-template-columns: none !important;
            grid-template-rows: none !important;
        }

        #grade-teacher .student-card {
            /* 设置固定宽度确保横向排列 */
            width: 200px !important;
            flex: 0 0 200px !important;
            flex-shrink: 0 !important;
            margin-bottom: 15px !important;
            display: inline-block !important;
            /* 其他样式继承自现有的学生卡片样式 */
        }

        #grade-teacher .student-card .check-in-time {
            position: absolute;
            bottom: 5px;
            left: 13px;
            background: #2e7d32;
            color: #ffffff;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            line-height: 1.2;
            white-space: nowrap;
            z-index: 2;
        }

        #grade-teacher .student-card .check-out-time {
            position: absolute;
            bottom: 5px;
            right: 13px;
            background: #ff4444;
            color: #ffffff;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            line-height: 1.2;
            white-space: nowrap;
            z-index: 2;
        }
        
        /* 确保教师卡片底部有足够空间显示时间 */
        #grade-teacher .student-card .school-info {
            margin-bottom: 25px;
            bottom: -18px; /* 向下移动23个像素 */
        }
        /* 显示学校信息模式下才显示 */
        .show-school .teacher-card .school-info {
            display: block;
        }

        /* 头像样式 */
        #modalStudentAvatar {
            cursor: pointer !important;
        }
        #modalStudentAvatar:hover {
            opacity: 0.8;
        }
        #modalStudentAvatar:hover::after {
            content: '点击更换照片';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            font-size: 12px;
            padding: 4px;
            text-align: center;
        }
        
        .student-avatar img {
            cursor: pointer !important;
        }
        .student-avatar img:hover {
            opacity: 0.8;
        }
        .student-avatar img.default-avatar {
            position: relative;
            z-index: 1;
        }
        .student-avatar img.default-avatar:hover::before {
            content: '点击上传照片';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 4px;
            white-space: nowrap;
            z-index: 2;
        }

        .loading-indicator {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            font-size: 16px;
            color: #666;
            position: relative;
            white-space: nowrap; /* 保证横向显示 */
        }

        .loading-indicator::after {
            content: '...';
            display: inline-block;
            width: 24px;
            text-align: left;
            animation: loadingDots 1s steps(3, end) infinite;
            margin-left: 8px;
            color: #1976d2;
        }

        @keyframes loadingDots {
            0%   { content: ''; }
            33%  { content: '.'; }
            66%  { content: '..'; }
            100% { content: '...'; }
        }
    </style>

    <!-- 添加头像样式脚本 -->
    <script>
        // 调试模式开关
        const DEBUG_MODE = false; // 设置为true启用调试输出

        // 调试日志函数
        function debugLog(...args) {
            if (DEBUG_MODE) {
                console.log(...args);
            }
        }

        // 事件委托管理器
        const EventManager = {
            delegates: new Map(),

            delegate(container, selector, event, handler) {
                const key = `${container}-${event}`;
                if (!this.delegates.has(key)) {
                    const containerElement = typeof container === 'string' ?
                        document.querySelector(container) : container;

                    if (containerElement) {
                        const delegateHandler = (e) => {
                            const target = e.target.closest(selector);
                            if (target) {
                                handler.call(target, e);
                            }
                        };

                        containerElement.addEventListener(event, delegateHandler);
                        this.delegates.set(key, { containerElement, delegateHandler });
                    }
                }
            },

            remove(container, event) {
                const key = `${container}-${event}`;
                const delegate = this.delegates.get(key);
                if (delegate) {
                    delegate.containerElement.removeEventListener(event, delegate.delegateHandler);
                    this.delegates.delete(key);
                }
            }
        };

        // 初始化学生头像样式
        function initStudentAvatarStyles() {
            const style = document.createElement('style');
            style.textContent = `
                #modalStudentAvatar {
                    cursor: pointer !important;
                }
                #modalStudentAvatar:hover {
                    opacity: 0.8;
                }
                #modalStudentAvatar:hover::after {
                    content: '点击更换照片';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    background: rgba(0, 0, 0, 0.7);
                    color: white;
                    font-size: 12px;
                    padding: 4px;
                    text-align: center;
                }

                .student-avatar img {
                    cursor: pointer !important;
                }
                .student-avatar img:hover {
                    opacity: 0.8;
                }
                .student-avatar img.default-avatar {
                    position: relative;
                    z-index: 1;
                }
                .student-avatar img.default-avatar:hover::before {
                    content: '点击上传照片';
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: rgba(0, 0, 0, 0.7);
                    color: white;
                    font-size: 12px;
                    padding: 4px 8px;
                    border-radius: 4px;
                    white-space: nowrap;
                    z-index: 2;
                }
            `;
            document.head.appendChild(style);
        }
    </script>
</head>
<body>
    <!-- 添加消息容器 -->
    <div class="message-container" id="messageContainer"></div>
    
    <!-- 添加调试信息显示区域 -->
    <div id="debug-info" style="
        position: fixed;
        top: 10px;
        left: 10px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 10px;
        border-radius: 5px;
        font-size: 14px;
        z-index: 9999;
        font-family: monospace;
    ">
        登录状态: 正在检查...
    </div>

    <div class="container">
        <div class="header">
            <!-- 登录信息显示 -->
            <div class="login-info">
                <div>用户：<span class="user" id="currentUserDisplay"></span>
                    <button class="logout-button" id="logoutButton">
                        退出登录
                    </button>
                </div>
                <div class="status-date"></div>
                <div class="status-lunar-time">
                    <span class="status-time" id="currentTime"></span>
                </div>
            </div>
            <div class="header-buttons">
                <div class="buttons-column">
                    <button class="header-button" id="adminToolsButton" onclick="showAdminToolsModal()">
                        <svg viewBox="0 0 24 24" width="16" height="16" fill="white">
                            <path d="M22.7 19l-9.1-9.1c.9-2.3.4-5-1.5-6.9-2-2-5-2.4-7.4-1.3L9 6 6 9 1.6 4.7C.4 7.1.9 10.1 2.9 12.1c1.9 1.9 4.6 2.4 6.9 1.5l9.1 9.1c.4.4 1 .4 1.4 0l2.3-2.3c.5-.4.5-1.1.1-1.4z"/>
                        </svg>
                        管理工具
                    </button>
                    <button class="header-button" id="attendanceQueryButton" onclick="showAttendanceQueryModal()">
                        <svg viewBox="0 0 24 24" width="16" height="16" fill="white">
                            <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
                        </svg>
                        签到查询
                    </button>
                </div>
                <div class="buttons-column">
                    <button class="header-button" id="ebookButton" onclick="window.open('xue.html', '_blank')">
                        <span style="font-size: 16px;">📚</span>
                        学习资料
                    </button>
                    <button class="header-button" id="studentManageButton" onclick="window.open('Media.html', '_blank')">
                        <svg viewBox="0 0 24 24" width="16" height="16" fill="white">
                            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0-6c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2zm0 7c-2.67 0-8 1.34-8 4v3h16v-3c0-2.66-5.33-4-8-4zm6 5H6v-.99c.2-.72 3.3-2.01 6-2.01s5.8 1.29 6 2v1z"/>
                        </svg>
                        英语音频
                    </button>
                </div>
            </div>
            <h1>
                <img src="./node_modules/Logo.png" alt="跃上教育" class="logo" id="ysLogo">
                <div class="title-text">
                    <span class="company-name">跃上教育<span class="registered-mark">®</span></span>
                    学生签到系统
                </div>
            </h1>
        </div>

        <div class="grade-tabs">
            <button class="grade-tab" data-grade="1"><script>document.write((function() { 
                // 使用已有的isSummerVacationPeriod函数判断
                if (typeof isSummerVacationPeriod === 'function' && isSummerVacationPeriod()) {
                    return '高一';
                } else {
                    return '一年级';
                }
            })());</script></button>
            <button class="grade-tab" data-grade="2">二年级</button>
            <button class="grade-tab" data-grade="3">三年级</button>
            <button class="grade-tab" data-grade="4">四年级</button>
            <button class="grade-tab" data-grade="5">五年级</button>
            <button class="grade-tab" data-grade="6">六年级</button>
            <button class="grade-tab" data-grade="7">七年级</button>
            <button class="grade-tab" data-grade="8">八年级</button>
            <button class="grade-tab" data-grade="9">九年级</button>
            <button class="grade-tab unsign-tab" data-grade="unsign">未签到汇总</button>
            <button class="grade-tab unsignout-tab" data-grade="unsignout">未签退汇总</button>
            <button class="display-mode-toggle" id="displayModeToggle">显示学校信息</button>
            <button class="search-button" onclick="showSearchModal()">
                <svg viewBox="0 0 24 24">
                    <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                </svg>
                搜索学生
            </button>
            <button class="grade-tab teacher-tab" data-grade="teacher">教师考勤</button>
        </div>

        <div id="grade-1" class="students-container"></div>
        <div id="grade-2" class="students-container"></div>
        <div id="grade-3" class="students-container"></div>
        <div id="grade-4" class="students-container"></div>
        <div id="grade-5" class="students-container"></div>
        <div id="grade-6" class="students-container"></div>
        <div id="grade-7" class="students-container"></div>
        <div id="grade-8" class="students-container"></div>
        <div id="grade-9" class="students-container"></div>
        <div id="grade-unsign" class="students-container">
            <div class="unsign-summary"></div>
        </div>
        <div id="grade-unsignout" class="students-container">
            <div class="unsign-summary"></div>
        </div>
        <div id="grade-teacher" class="students-container">
            <div class="teacher-cards"></div>
        </div>
    </div>

    <!-- 在 body 标签的 container div 后面添加弹窗组件 -->
    <div id="studentModal" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <div class="student-detail">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                    <div class="time-display" style="text-align: right; flex: 1;">
                        <div id="modalSignInTime" class="time-status" title="点击进行签到" onclick="handleModalTimeClick('签到')"></div>
                    </div>
                    <div class="student-detail-avatar" style="margin: 0 20px;">
                    <img src="" alt="学生照片" id="modalStudentAvatar">
                    </div>
                    <div class="time-display" style="text-align: left; flex: 1;">
                        <div id="modalSignOutTime" class="time-status" title="点击进行签退" onclick="handleModalTimeClick('签退')"></div>
                    </div>
                </div>
                <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 15px; position: relative;">
                    <h2 id="modalStudentName" style="margin: 0;"></h2>
                    <button id="deleteStudentBtn" onclick="deleteStudentFromGrade()" 
                            style="display: none; position: absolute; right: 20px; top: 55%; transform: translateY(-50%); background: #ff4444; color: white; border: none; padding: 2px 8px; 
                                   border-radius: 4px; cursor: pointer; font-size: 15px;">
                        删除学生
                    </button>
                </div>
                <div class="student-info">
                    <!-- 学生信息字段组：工号 -->
                    <p style="display: flex; align-items: center;">
                        <strong>工号：</strong>
                        <span id="modalPinyin" style="flex: 1; margin-right: 10px;"></span>
                        <button id="showAttendanceRecords" class="btn btn-info" style="white-space: nowrap; font-size: 13px; padding: 4px 10px; border-radius: 4px;">
                            <svg viewBox="0 0 16 16" width="14" height="14" style="vertical-align: -2px; margin-right: 3px;">
                                <path fill="currentColor" d="M12.5 0h-9C2.67 0 2 .67 2 1.5v13c0 .83.67 1.5 1.5 1.5h9c.83 0 1.5-.67 1.5-1.5v-13c0-.83-.67-1.5-1.5-1.5zm.5 14.5c0 .28-.22.5-.5.5h-9c-.28 0-.5-.22-.5-.5v-13c0-.28.22-.5.5-.5h9c.28 0 .5.22.5.5v13z"/>
                                <path fill="currentColor" d="M11.5 5h-7c-.28 0-.5.22-.5.5s.22.5.5.5h7c.28 0 .5-.22.5-.5s-.22-.5-.5-.5zM11.5 7h-7c-.28 0-.5.22-.5.5s.22.5.5.5h7c.28 0 .5-.22.5-.5s-.22-.5-.5-.5zM11.5 9h-7c-.28 0-.5.22-.5.5s.22.5.5.5h7c.28 0 .5-.22.5-.5s-.22-.5-.5-.5zM11.5 11h-7c-.28 0-.5.22-.5.5s.22.5.5.5h7c.28 0 .5-.22.5-.5s-.22-.5-.5-.5z"/>
                            </svg>
                            签到记录
                        </button>
                    </p>
                    <p style="display: flex; align-items: center;">
                        <strong>学校：</strong>
                        <input list="schoolList" id="modalSchool" type="text" style="flex: 1; margin-right: 8px;">
                        <button class="search-classmates-btn" onclick="searchClassmates('school')" title="搜索同学校学生" style="background: #4CAF50; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 12px; white-space: nowrap;">
                            同校
                        </button>
                        <datalist id="schoolList">
                            <option value="滨河实验">
                            <option value="东苑中学">
                            <option value="怡景小学">
                            <option value="七中">
                            <option value="英特">
                            <option value="文轩中学">
                            <option value="百草园小学">
                            <option value="外国语小学">
                            <option value="运河实验">
                            <option value="育红小学">
                            <option value="东昌中学">
                            <option value="文苑中学">
                            <option value="水城慧德">
                            <option value="光明小学">
                            <option value="东昌路小学">
                            <option value="振兴路小学">
                            <option value="兴华路小学">
                            <option value="风貌街小学">
                            <option value="开发区实验">
                        </datalist>
                    </p>
                    <p style="display: flex; align-items: center;">
                        <strong>年级：</strong>
                        <input list="gradeList" id="modalGrade" type="text" style="flex: 1; margin-right: 8px;">
                        <button class="search-classmates-btn" onclick="searchClassmates('grade')" title="搜索同年级学生" style="background: #9C27B0; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 12px; white-space: nowrap;">
                            同级
                        </button>
                        <datalist id="gradeList">
                            <option value="1年级">
                            <option value="2年级">
                            <option value="3年级">
                            <option value="4年级">
                            <option value="5年级">
                            <option value="6年级">
                            <option value="7年级">
                            <option value="8年级">
                            <option value="9年级">
                        </datalist>
                    </p>
                    <p style="display: flex; align-items: center;">
                        <strong>班级：</strong>
                        <input type="number" id="modalClass" min="0" max="99" style="flex: 1; margin-right: 8px;">
                        <button class="search-classmates-btn" onclick="searchClassmates('class')" title="搜索同班学生" style="background: #2196F3; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 12px; white-space: nowrap;">
                            同班
                        </button>
                    </p>
                    <div id="phoneContainer">
                        <p style="display: flex; align-items: center; margin-bottom: 0;">
                            <strong>电话：</strong>
                            <div id="phoneList" style="flex: 1; margin-left: 2px;">
                                <!-- 电话号码列表将通过JavaScript动态添加 -->
                            </div>
                            <button class="phone-action-btn phone-add-btn" onclick="addPhoneNumber()" title="添加新电话" style="margin-left: 5px;">
                                +
                            </button>
                        </p>
                    </div>
                    <p style="display: flex; align-items: center;">
                        <strong>住址：</strong>
                        <input type="text" id="modalAddress" style="flex: 1; margin-right: 8px;">
                        <button class="search-classmates-btn" onclick="searchClassmates('address')" title="搜索同小区学生" style="background: #FF9800; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 12px; white-space: nowrap; margin-right: 8px;">
                            同小区
                        </button>
                        <a class="icon-link location-link" title="查看地图">
                            <svg viewBox="0 0 24 24">
                                <path fill="currentColor" d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                            </svg>
                        </a>
                    </p>
                    <p>
                        <strong>备注：</strong>
                        <textarea id="modalRemark" placeholder="请输入备注信息"></textarea>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- 修改底部状态栏 -->
    <div class="status-bar">
        <div class="status-left">
            <span class="status-grade"></span>
            <span class="status-total"></span>
            <span class="status-signed"></span>
            <span class="status-unsigned"></span>
            <span class="status-leave"></span>
        </div>
        <div class="status-center" id="selectedCount"></div>
        <div class="status-right">
            <span class="status-date"></span>
            <span class="lunar-date"></span>
        </div>
    </div>

    <!-- 添加签到时间详情模态框 -->
    <div id="timeDetailsModal" class="modal" style="display: none;">
        <div class="modal-content time-details-modal" style="max-height: 80vh; overflow-y: auto;">
            <span class="close-modal" onclick="document.getElementById('timeDetailsModal').style.display='none'">&times;</span>
            <h2 id="timeDetailsTitle" style="margin-bottom: 20px; color: #1976d2;"></h2>
            <div id="timeDetailsList" style="font-size: 14px; line-height: 1.6;"></div>
        </div>
    </div>

    <!-- 同班同学搜索结果模态框 -->
    <div id="classmatesModal" class="modal" style="display: none;">
        <div class="modal-content" style="width: 90%; max-width: 600px; max-height: 80vh; overflow-y: auto; padding-bottom: 30px;">
            <span class="close-modal" onclick="document.getElementById('classmatesModal').style.display='none'">&times;</span>
            <h3 id="classmatesModalTitle" style="margin-top: 0; margin-bottom: 20px; color: #1976d2; text-align: center;">搜索结果</h3>

            <!-- 标签页导航 -->
            <div class="classmates-tabs">
                <button class="classmates-tab active" data-tab="classmates">
                    同班学生 (<span id="classmatesCount">0</span>)
                </button>
                <button class="classmates-tab" data-tab="grademates">
                    同年级学生 (<span id="gradematesCount">0</span>)
                </button>
                <button class="classmates-tab" data-tab="schoolmates">
                    同学校学生 (<span id="schoolmatesCount">0</span>)
                </button>
                <button class="classmates-tab" data-tab="neighbors">
                    同小区学生 (<span id="neighborsCount">0</span>)
                </button>
            </div>

            <!-- 标签页内容 -->
            <div id="classmatesContent" class="classmates-tab-content active" style="margin-bottom: 20px;">
                <div id="classmatesList" class="classmates-list"></div>
            </div>
            <div id="gradematesContent" class="classmates-tab-content" style="display: none; margin-bottom: 20px;">
                <div id="gradematesList" class="classmates-list"></div>
            </div>
            <div id="schoolmatesContent" class="classmates-tab-content" style="display: none; margin-bottom: 20px;">
                <div id="schoolmatesList" class="classmates-list"></div>
            </div>
            <div id="neighborsContent" class="classmates-tab-content" style="display: none; margin-bottom: 20px;">
                <div id="neighborsList" class="classmates-list"></div>
            </div>
        </div>
    </div>

    <!-- 添加搜索模态框 -->
    <div id="searchModal" class="modal" style="display: none;">
        <div class="modal-content search-modal-content">
            <span class="close-modal">&times;</span>
            <h3 style="margin: 0 0 25px 0; color: #333; text-align: center;">搜索学生</h3>
            <div class="search-input-container">
                <input type="text" class="search-input" id="searchInput" placeholder="输入姓名或工号搜索..." oninput="handleSearch()">
            </div>
            <div class="search-results" id="searchResults"></div>
        </div>
    </div>

    <!-- 签到记录查询模态框 -->
    <div id="attendanceModal" class="modal" style="display: none; z-index: 1001;">
        <div class="modal-content" style="width: 90%; max-width: 800px; max-height: 80vh; overflow-y: auto; position: absolute; top: 10%; left: 50%; transform: translateX(-50%);">
            <div class="modal-header">
                <h2>签到记录查询</h2>
                <span class="close-modal">&times;</span>
            </div>
            <div id="attendance-query-container" class="p-3">
                <!-- 签到记录查询界面将由JS动态创建 -->
            </div>
        </div>
    </div>

    <script>
    // 添加签到查询模态框显示函数
    function showAttendanceQueryModal() {
        const attendanceModal = document.getElementById('attendanceModal');
        if (attendanceModal) {
            attendanceModal.style.display = 'block';
        } else {
            showToast('签到查询模块正在加载中，请稍候...', 'info');
        }
    }
        
    // 移除旧的调试信息显示区域
        const debugDiv = document.getElementById('debug-info');
        if (debugDiv) {
        debugDiv.remove();
    }

    // 判断当前日期是否在暑假班期间（7月3日至8月31日）
    function isSummerVacationPeriod() {
        const now = new Date();
        const month = now.getMonth() + 1; // getMonth() 返回 0-11
        const day = now.getDate();

        // 检查是否在7月3日至8月31日之间
        if ((month === 7 && day >= 3) || (month === 8 && day <= 31)) {
            return true;
        }
        return false;
    }
    
    // 为年级按钮添加"新"角标
    function addNewBadgeToGradeButtons() {
        // 只有在暑假班期间才添加角标
        if (!isSummerVacationPeriod()) {
            return;
        }
        
        // 获取所有年级按钮（排除特殊按钮如未签到汇总、教师考勤等）
        const gradeButtons = document.querySelectorAll('.grade-tab:not(.unsign-tab):not(.unsignout-tab):not(.teacher-tab)');
        
        gradeButtons.forEach(button => {
            const grade = button.getAttribute('data-grade');
            // 只为普通年级按钮添加角标（1-9年级）
            if (grade && !isNaN(grade) && grade >= 1 && grade <= 9) {
                // 创建角标元素
                const badge = document.createElement('span');
                badge.className = 'grade-new-badge';
                badge.textContent = '新';
                
                // 添加到按钮中
                button.appendChild(badge);
                
                // 特殊处理：一年级按钮在暑假班期间显示为"新高一"
                if (grade === '1') {
                    // 先清空按钮内容
                    button.textContent = '';
                    // 添加新文本
                    const textNode = document.createTextNode('高一');
                    button.appendChild(textNode);
                    // 重新添加角标
                    button.appendChild(badge);
                }
            }
        });
    }

    // 更新登录信息显示函数
    function updateLoginInfo() {
        const currentUserDisplay = document.getElementById('currentUserDisplay');
        
        if (currentUserDisplay) {
            const currentUser = localStorage.getItem('currentUser');
            currentUserDisplay.textContent = currentUser === 'ling' ? '管理员' : `${currentUser}年级教师`;
        }
    }

    // 页面加载完成后执行
    window.onload = function() {
        try {
            // 设置动态Meta标签
            const baseUrl = window.location.href.substring(0, window.location.href.lastIndexOf('/') + 1);
            document.querySelector('meta[property="og:url"]').setAttribute('content', window.location.href);
            document.querySelector('meta[property="og:image"]').setAttribute('content', baseUrl + 'node_modules/LOGO.png');
            document.querySelector('meta[itemprop="image"]').setAttribute('content', baseUrl + 'node_modules/LOGO.png');

            // 从 localStorage 获取用户名
            const currentUser = localStorage.getItem('currentUser');
            if (!currentUser) {
                window.location.href = 'login.html';
                return;
            }

            // 更新登录信息显示
            updateLoginInfo();

            // 为年级按钮添加"新"角标（暑假班期间）
            addNewBadgeToGradeButtons();

            // 时间更新已在页面底部统一初始化

            // 状态栏点击事件已在DOMContentLoaded中统一绑定
            
            if (currentUser === 'ling') {
                // 管理员权限处理
            } else {
                const grade = currentUser;  // 现在用户名就是年级数字
                
                // 延迟执行以确保页面元素已加载
                setTimeout(function() {
                    // 隐藏除了当前年级和未签到汇总之外的所有年级按钮
                    const allGradeBtns = document.querySelectorAll('.grade-tab');
                    allGradeBtns.forEach(btn => {
                        const btnGrade = btn.getAttribute('data-grade'); // 此处控制非管理员显示的按钮
                        if (btnGrade !== grade && btnGrade !== 'unsign' && btnGrade !== 'unsignout' && btnGrade !== 'teacher') {
                            btn.style.display = 'none';
                        }
                    });
                    
                    // 查找并点击对应年级的按钮
                    const gradeBtn = document.querySelector(`.grade-tab[data-grade="${grade}"]`);
                    if (gradeBtn) {
                        gradeBtn.click();
                    }
                }, 1000);
            }
        } catch (error) {
            console.error('[系统] 检查登录状态出错:', error);
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 2000);
        }
    };

    // 添加LOGO点击事件，跳转到跃上教育主页
    const ysLogo = document.getElementById('ysLogo');
    if (ysLogo) {
        ysLogo.style.cursor = 'pointer';
        ysLogo.title = '点击访问跃上教育主页';
        ysLogo.onclick = function(e) {
            window.open('https://ys0635.cn/ys.html', '_blank');
        };
    }
    </script>

    <!-- 检查登录状态 -->
    <script>
        // 检查登录状态
        if (localStorage.getItem('isLoggedIn') !== 'true') {
            window.location.href = 'login.html';
        }
    </script>

    <script>
        // 全局变量
        window.studentsData = {};      // 年级-学生映射
        window.studentDetails = {};    // 学生详细信息
        window.nameToPinyin = {};     // 姓名-拼音映射
        let studentPoints = {};     // 学生积分信息

        let leaveStudents = new Set();  // 存储请假学生姓名
        
        // 添加教师相关全局变量
        window.teacherDetails = {};  // 使用window声明以避免重新赋值
        window.teacherDataLoaded = false; // 使用全局变量跟踪教师数据是否已经加载过

        // 修改音频播放相关代码
        let audioMap = new Map();  // 用于跟踪每个音频文件的播放状态

        function playAudio(name) {
            // 使用新的API端点获取音频文件
            const audioPath = `/get-bobao-audio/${name}.mp3`;
            
            // 检查是否已经在播放这个音频
            if (audioMap.has(name)) {
                const existingAudio = audioMap.get(name);
                if (!existingAudio.ended) {
                    return;
                } else {
                    audioMap.delete(name);
                }
            }

            // 创建新的音频对象
            const audio = new Audio(audioPath);
            
            // 添加到Map中
            audioMap.set(name, audio);
            
            // 添加加载事件监听
            audio.addEventListener('loadstart', () => {});

            audio.addEventListener('canplaythrough', () => {});

            // 播放完成后从Map中删除
            audio.onended = () => {
                audioMap.delete(name);
            };

            // 播放出错时也从Map中删除
            audio.onerror = (e) => {
                audioMap.delete(name);
                console.error(`音频加载失败: ${name}.mp3`, e);
            };

            // 开始播放
            audio.play().catch(error => {
                console.error(`音频播放失败: ${name}.mp3`, error);
                audioMap.delete(name);
            });
        }

        // 主数据加载函数
        async function loadStudentData() {
            try {
                // 获取当前用户身份
                const currentUser = localStorage.getItem('currentUser');
                const isAdmin = currentUser === 'ling';
                
                // 1. 加载拼音数据（仍然需要）
                await loadPinyinData();
                
                // 2-6. 根据用户身份加载不同范围的数据
                if (isAdmin) {
                    // 管理员加载所有数据
                    await loadAttendanceFilePaths();
                    await loadStudentInfo();
                    await loadStudentPoints();
                    await loadAttendanceInfo();
                    await loadLeaveInfo();
                    
                    // 初始化默认年级（二年级）
                    await initializeGradeCards('2');
                    
                    // 更新显示状态
                    updateCurrentGradeDisplay();
                    
                    // 异步加载其他年级
                    loadOtherGradesAsync('2');
                } else {
                    // 非管理员只加载当前年级
                    const grade = currentUser;
                    
                    // 隐藏其他年级按钮，只显示当前年级和未签到汇总
                    hideOtherGradeButtons(grade);
                    
                    // 只加载当前年级的数据
                    await loadSingleGradeAttendanceFilePaths(grade);
                    await loadSingleGradeStudentInfo(grade);
                    await loadStudentPoints(); // 仍然需要加载所有学分，但只会用到当前年级学生的
                    await loadAttendanceInfo(); // 仍然需要加载考勤信息，但只会用到当前年级学生的
                    await loadLeaveInfo(); // 仍然需要加载请假信息，但只会用到当前年级学生的
                    
                    // 只初始化当前年级
                    await initializeGradeCards(grade);
                    
                    // 更新显示状态
                    updateCurrentGradeDisplay();
                }
            } catch (error) {
                alert(`加载数据失败：${error.message}`);
            }
        }

        // 新增：隐藏其他年级按钮，只保留当前年级、未签到汇总和教师考勤
        function hideOtherGradeButtons(currentGrade) {
            // 隐藏所有年级容器
            for (let grade = 1; grade <= 9; grade++) {
                if (grade.toString() !== currentGrade) {
                    const container = document.getElementById(`grade-${grade}`);
                    if (container) {
                        container.style.display = 'none';
                        // 重要：设置空内容以防止后续可能的操作尝试访问
                        container.innerHTML = '';
                    }
                }
            }
            
            // 隐藏所有不相关的年级按钮
            document.querySelectorAll('.grade-tab').forEach(tab => {
                const grade = tab.getAttribute('data-grade');
                // 只保留当前年级、未签到汇总、未签退汇总和教师考勤按钮
                if (grade !== currentGrade && grade !== 'unsign' && grade !== 'unsignout' && 
                    grade !== 'teacher') {
                    tab.style.display = 'none';
                }
            });
            
            // 确保当前年级按钮处于激活状态
            const currentGradeTab = document.querySelector(`.grade-tab[data-grade="${currentGrade}"]`);
            if (currentGradeTab) {
                document.querySelectorAll('.grade-tab').forEach(t => t.classList.remove('active'));
                currentGradeTab.classList.add('active');
            }
            
            // 设置当前年级全局变量
            currentGrade = currentGrade;
        }

        // 修改年级切换处理函数，防止非管理员访问其他年级
        document.querySelectorAll('.grade-tab').forEach(tab => {
            tab.addEventListener('click', async (e) => {
                const selectedGrade = tab.getAttribute('data-grade');

            // 非管理员只允许访问自己年级、未签到汇总、未签退汇总和教师考勤
            if (!AppState.isAdmin && selectedGrade !== AppState.currentUser &&
                selectedGrade !== 'unsign' && selectedGrade !== 'unsignout' &&
                selectedGrade !== 'teacher') {
                return; // 不允许切换到其他年级
            }

            // 移除所有标签的选中状态
            document.querySelectorAll('.grade-tab').forEach(t => t.classList.remove('active'));
            // 添加当前标签的选中状态
            tab.classList.add('active');

            // 更新应用状态
            AppState.setCurrentGrade(selectedGrade);

            // 重置选中状态
            document.querySelectorAll('.student-card.selected').forEach(card => {
                    card.classList.remove('selected');
                    const points = card.querySelector('.student-points');
                    const infoButton = card.querySelector('.info-button');
                    
                    if (points) {
                        points.style.opacity = '0';
                        points.style.transform = 'translateY(-10px)';
                    }
                    if (infoButton) {
                        infoButton.style.opacity = '0';
                        infoButton.style.transform = 'translateY(-10px)';
                    }
                });
                
                // 更新选中数量显示
                updateSelectedCount();
                
                // 更新当前年级
                currentGrade = selectedGrade;
                
            // 如果是普通年级且数据未加载，则加载数据（仅限管理员）
            if (AppState.isAdmin && selectedGrade !== 'unsign' && selectedGrade !== 'unsignout' && selectedGrade !== 'teacher') {
                const container = document.getElementById(`grade-${selectedGrade}`);
                if (container && !container.getAttribute('data-loaded')) {
                    container.innerHTML = '<div class="loading-indicator">正在加载</div>';
                    await initializeGradeCards(selectedGrade);
                }
            }

            // 特殊处理教师年级布局
            if (selectedGrade === 'teacher') {
                    
                    // 显示教师容器并应用正确的布局
                    const teacherContainer = document.getElementById('grade-teacher');
                    if (teacherContainer) {
                        // 强制使用block布局而非grid
                        teacherContainer.style.display = 'block';
                    }
                    
                    // 确保教师卡片容器使用flex布局
                    const cardsContainer = document.querySelector('#grade-teacher .teacher-cards');
                    if (cardsContainer) {
                        // 设置必要的样式
                        cardsContainer.style.display = 'flex';
                        cardsContainer.style.flexWrap = 'wrap';
                        cardsContainer.style.flexDirection = 'row';
                        cardsContainer.style.justifyContent = 'flex-start';
                        cardsContainer.style.gap = '15px';
                        cardsContainer.style.padding = '0px 20px 20px 20px';
                        
                        // 只在首次加载或需要刷新时才加载教师数据
                        if (!window.teacherDataLoaded || Object.keys(teacherDetails).length === 0) {
                            // 延迟一下再加载教师数据，确保布局已应用
                            // 【修正开始】修改为先加载考勤数据，再传递给 loadTeacherData
                            setTimeout(async () => {
                                try {
                                    // 在这里重新调用 loadAttendanceInfo 获取签到签退数据
                                    const { signInContent, signOutContent } = await loadAttendanceInfo(); 
                                    await loadTeacherData(signInContent, signOutContent, true); // 首次加载需要工号信息
                                    
                                    // 标记教师数据已加载
                                    window.teacherDataLoaded = true;
                                    
                                    // 更新教师状态栏
                                    updateTeacherStatusBarNew();
                                } catch (error) {
                                    console.error('[年级切换] 加载教师数据失败:', error);
                                    showMessage('加载教师数据失败: ' + error.message, 'error');
                                }
                            }, 50);
                            // 【修正结束】
                        } else {
                            // 数据已加载，不必重新加载，只更新状态栏
                            updateTeacherStatusBarNew();
                            
                            // 如果需要重新渲染卡片（比如状态有更新），可以调用
                            renderTeacherCards();
                        }
                    }
            }
            // 更新显示
            updateCurrentGradeDisplay();
            });
        });

        // 只加载指定年级的考勤文件路径
        async function loadSingleGradeAttendanceFilePaths(grade) {
            try {
                // 初始化年级数据结构
                studentsData[grade] = [];
                
                // 使用现有的API获取年级学生数据 - 使用attendance API的各年级参数
                const response = await fetch(`/api/attendance?各年级=true`);
                if (!response.ok) {
                    throw new Error(`获取${grade}年级学生列表失败`);
                }
                
                const data = await response.json();
                if (data && data[grade] && Array.isArray(data[grade]) && data[grade].length > 0) {
                    // 存储该年级的学生列表
                    studentsData[grade] = data[grade];
                    //console.log(`已加载${grade}年级的${data[grade].length}名学生数据`);
                } else {
                    console.warn(`${grade}年级没有学生数据`);
                }
            } catch (error) {
                console.error('加载年级数据失败：', error);
                throw new Error(`加载${grade}年级数据失败: ${error.message}`);
            }
        }

        // 加载所有年级的学生数据 (管理员使用)
        async function loadAttendanceFilePaths() {
            try {
                // 使用新API获取所有年级学生数据
                const response = await fetch('/api/attendance?各年级=true');
                if (!response.ok) {
                    throw new Error('获取年级学生数据失败');
                }
                
                // 直接获取JSON格式的年级和学生信息
                const gradeData = await response.json();
                
                // 更新studentsData对象
                Object.keys(gradeData).forEach(grade => {
                    studentsData[grade] = gradeData[grade];
                    //console.log(`已加载${grade}年级的${gradeData[grade].length}名学生数据`);
                });

            } catch (error) {
                console.error('加载年级学生数据失败:', error);
                throw new Error('加载年级学生数据失败');
            }
        }
        
        // 只加载指定年级的学生基本信息
        async function loadSingleGradeStudentInfo(grade) {
            try {
                // 获取当前年级的学生列表
                const students = studentsData[grade] || [];
                if (students.length === 0) {
                    console.warn(`${grade}年级没有学生，跳过加载学生信息`);
                    return;
                }
                
                // 构建包含所有学生姓名的请求URL，使用新API格式
                const queryParams = students.map(name => `name=${encodeURIComponent(name)}`).join('&');
                const response = await fetch(`/api/student-info?${queryParams}`);
                
                if (!response.ok) {
                    throw new Error(`获取学生详情失败: ${response.status}`);
                }
                
                // 新API返回JSON格式而非文本
                const data = await response.json();
                
                if (!data.success) {
                    console.error('API返回错误:', data.message || data.error);
                    throw new Error(data.message || data.error || '获取学生详情失败');
                }
                
                // 处理JSON格式的学生信息
                let processedCount = 0;
                
                if (data.students) {
                    for (const [name, info] of Object.entries(data.students)) {
                        // 确保学生信息对象存在
                        if (!studentDetails[name]) {
                            studentDetails[name] = {
                                '学校': '',
                                '年级': '',
                                '班级': '',
                                '电话': [],
                                '住址': '',
                                '备注': ''
                            };
                        }
                        
                        // 填充学生信息
                        studentDetails[name]['学校'] = info.school || '';
                        studentDetails[name]['年级'] = info.grade || '';
                        studentDetails[name]['班级'] = info.class || '';
                        studentDetails[name]['电话'] = info.phone ? info.phone.split('&').filter(p => p.trim()) : [];
                        studentDetails[name]['住址'] = info.address || '';
                        studentDetails[name]['备注'] = info.notes || '';
                        processedCount++;
                    }
                }
                
                //console.log(`已加载${grade}年级的${processedCount}名学生详情`);
            } catch (error) {
                console.error('加载学生详情失败：', error);
                throw new Error(`加载${grade}年级学生详情失败: ${error.message}`);
            }
        }

        // 加载学生基本信息
        async function loadStudentInfo() {
            try {
                // 直接使用已加载的studentsData对象获取所有学生
                const allStudents = [];
                for (const grade in studentsData) {
                    if (Array.isArray(studentsData[grade])) {
                        allStudents.push(...studentsData[grade]);
                    }
                }
                
                if (allStudents.length === 0) {
                    console.warn('未获取到学生名单，无法加载学生信息');
                    return;
                }
                
                // 按每批次50个学生分批请求，避免URL过长
                const batchSize = 50;
                for (let i = 0; i < allStudents.length; i += batchSize) {
                    const batchStudents = allStudents.slice(i, i + batchSize);
                    if (batchStudents.length === 0) continue;
                    
                    // 构建查询参数
                    const queryParams = batchStudents.map(name => `name=${encodeURIComponent(name)}`).join('&');
                    const response = await fetch(`/api/student-info?${queryParams}`);
                    
                    if (!response.ok) {
                        console.error(`第${i/batchSize + 1}批学生信息获取失败: ${response.status}`);
                        continue;
                    }
                    
                    const data = await response.json();
                    if (!data.success) {
                        console.error('API返回错误:', data.message || data.error);
                        continue;
                    }
                    
                    // 处理这一批次的学生信息
                    if (data.students) {
                        for (const [name, info] of Object.entries(data.students)) {
                            studentDetails[name] = {
                                '学校': info.school || '',
                                '年级': info.grade || '',
                                '班级': info.class || '',
                                '电话': info.phone ? info.phone.split('&').filter(p => p.trim()) : [],
                                '住址': info.address || '',
                                '备注': info.notes || ''
                            };
                        }
                    }
                }
            } catch (error) {
                console.error('加载学生基本信息失败:', error);
                //throw new Error('加载学生基本信息失败');
            }
        }

        // 初始化指定年级的卡片
        async function initializeGradeCards(grade) {
            const container = document.getElementById(`grade-${grade}`);
            if (container && studentsData[grade]) {
                const fragment = document.createDocumentFragment();
                
                studentsData[grade].forEach(name => {
                    const studentInfo = studentDetails[name] || {};
                    const pinyin = nameToPinyin[name] || '';
                    const points = studentPoints[name] || '0';
                    const checkInTime = studentInfo['签到'] ? formatTimeForCard(studentInfo['签到']) : '--:--';
                    const checkOutTime = studentInfo['签退'] ? formatTimeForCard(studentInfo['签退']) : '--:--';
                    
                    const card = document.createElement('div');
                    card.className = `student-card${studentInfo['签到'] ? ' signed' : ''}`;
                    card.setAttribute('data-name', name);
                    
                    card.innerHTML = `
                        <div class="student-points">${points}</div>
                        <div class="student-avatar">
                            <img src="./FaceID/学生照片/${pinyin}.jpg?t=${new Date().getTime()}" 
                                 alt="${name}" 
                                 onerror="this.src='./node_modules/默认头像.png'">
                        </div>
                        <div class="student-name">${name}</div>
                        <div class="time-info">
                            ${checkInTime !== '--:--' ? `<span class="check-in-time">${checkInTime}</span>` : ''}
                            ${checkOutTime !== '--:--' ? `<span class="check-out-time">${checkOutTime}</span>` : ''}
                        </div>
                        <div class="school-info">
                            ${studentDetails[name]?.['学校'] || ''} ${studentDetails[name]?.['年级'] || ''} ${studentDetails[name]?.['班级'] || ''}班
                        </div>
                        <button class="info-button" aria-label="查看学生信息">
                            <svg viewBox="0 0 24 24" width="20" height="20">
                                <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
                            </svg>
                        </button>
                    `;
                    
                    bindCardEvents(card, name);
                    fragment.appendChild(card);
                });
                
                container.innerHTML = '';
                container.appendChild(fragment);
                container.setAttribute('data-loaded', 'true');
            }
        }

        // 异步加载其他年级的卡片
        async function loadOtherGradesAsync(defaultGrade) {
            for (let grade = 1; grade <= 9; grade++) {
                if (grade.toString() !== defaultGrade) {
                    const container = document.getElementById(`grade-${grade}`);
                    if (container && !container.getAttribute('data-loaded')) {
                        // 添加加载中的提示
                        container.innerHTML = '<div class="loading-indicator">正在加载</div>';
                        
                        // 使用setTimeout来错开加载时间，避免同时加载造成卡顿
                        await new Promise(resolve => setTimeout(resolve, 100));
                        await initializeGradeCards(grade);
                    }
                }
            }
        }

        // 添加专门用于卡片显示的时间格式化函数
        function formatTimeForCard(timeStr) {
            if (!timeStr || timeStr === true) return '--:--';
            
            // 如果是布尔值true（强制签到/签退的情况），返回当前时间
            if (typeof timeStr === 'boolean') {
                const now = new Date();
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                return `${hours}:${minutes}`;
            }
            
            // 处理正常的时间字符串
            const match = String(timeStr).match(/(\d+)点(\d+)分(\d+)秒/);
                            if (match) {
                const [_, hours, minutes] = match;
                return `${hours.padStart(2, '0')}:${minutes.padStart(2, '0')}`;
            }
            return '--:--';
        }

        // 更新当前年级显示
        function updateCurrentGradeDisplay() {
            // 隐藏所有年级容器
            document.querySelectorAll('.students-container').forEach(container => {
                container.style.display = 'none';
            });
            
            // 显示当前年级
            if (currentGrade === 'unsign') {
                const container = document.getElementById('grade-unsign');
                container.style.display = 'block';

                // 检查是否需要更新未签到汇总
                const currentUnsignedStudents = getAllUnsignedStudents();
                const existingCards = Array.from(container.querySelectorAll('.unsign-summary .student-card'))
                    .map(card => card.getAttribute('data-name'));

                // 保存当前选中的学生
                const selectedStudents = Array.from(container.querySelectorAll('.student-card.selected'))
                    .map(card => card.getAttribute('data-name'));

                if (!haveSameElements(currentUnsignedStudents, existingCards)) {
                    updateUnsignDisplay();
                    // 恢复选中状态
                    selectedStudents.forEach(name => {
                        const card = container.querySelector(`.student-card[data-name="${name}"]`);
                        if (card) {
                            card.classList.add('selected');
                            const points = card.querySelector('.student-points');
                            const infoButton = card.querySelector('.info-button');
                            if (points) {
                                points.style.opacity = '1';
                                points.style.transform = 'translateY(0)';
                            }
                            if (infoButton) {
                                infoButton.style.opacity = '1';
                                infoButton.style.transform = 'translateY(0)';
                            }
                        }
                    });
                }
                updateStatusBarAll('unsign');
            } else if (currentGrade === 'unsignout') {
                const container = document.getElementById('grade-unsignout');
                container.style.display = 'block';

                // 检查是否需要更新未签退汇总
                const currentUnsignoutStudents = getAllUnsignoutStudents();
                const existingCards = Array.from(container.querySelectorAll('.unsign-summary .student-card'))
                    .map(card => card.getAttribute('data-name'));

                // 保存当前选中的学生
                const selectedStudents = Array.from(container.querySelectorAll('.student-card.selected'))
                    .map(card => card.getAttribute('data-name'));

                if (!haveSameElements(currentUnsignoutStudents, existingCards)) {
                    updateUnsignoutDisplay();
                    // 恢复选中状态
                    selectedStudents.forEach(name => {
                        const card = container.querySelector(`.student-card[data-name="${name}"]`);
                        if (card) {
                            card.classList.add('selected');
                            const points = card.querySelector('.student-points');
                            const infoButton = card.querySelector('.info-button');
                            if (points) {
                                points.style.opacity = '1';
                                points.style.transform = 'translateY(0)';
                            }
                            if (infoButton) {
                                infoButton.style.opacity = '1';
                                infoButton.style.transform = 'translateY(0)';
                            }
                        }
                    });
                }

                updateStatusBarAll('unsignout');
                } else if (currentGrade === 'teacher') {
                // 显示教师考勤容器
                const container = document.getElementById('grade-teacher');
                container.style.display = 'block';
                
                // 确保教师卡片容器使用flex布局
                const cardsContainer = document.querySelector('#grade-teacher .teacher-cards');
                if (cardsContainer) {
                    cardsContainer.style.display = 'flex';
                    cardsContainer.style.flexWrap = 'wrap';
                    cardsContainer.style.flexDirection = 'row';
                    cardsContainer.style.justifyContent = 'flex-start';
                    cardsContainer.style.gap = '15px';
                    cardsContainer.style.padding = '0px 20px 20px 20px';
                }
                
                // 加载教师数据并渲染教师卡片
                if (!teacherDataLoaded || Object.keys(teacherDetails).length === 0) {
                    setTimeout(async () => {
                        try {
                            //await loadTeacherData();
                            //teacherDataLoaded = true;
                            updateTeacherStatusBarNew();
                        } catch (error) {
                            console.error('[年级切换] 加载教师数据失败:', error);
                        }
                    }, 50);
                } else {
                    // 如果已有数据，只更新状态栏和重新渲染卡片
                    updateTeacherStatusBarNew();
                    renderTeacherCards();
                }
            } else {
                const container = document.getElementById(`grade-${currentGrade}`);
                if (container) {
                    container.style.display = 'grid';
                    // 确保请假状态正确显示
                    container.querySelectorAll('.student-card').forEach(card => {
                        const name = card.getAttribute('data-name');
                        if (name && leaveStudents.has(name)) {
                            card.classList.add('on-leave');
                            // 确保请假标记存在
                            if (!card.querySelector('.leave-badge')) {
                                const leaveBadge = document.createElement('div');
                                leaveBadge.className = 'leave-badge';
                                leaveBadge.textContent = '假';
                                card.appendChild(leaveBadge);
                            }
                        }
                    });
                    updateStatusBar(currentGrade);
                }
            }
            
            // 更新选中数量显示
            updateSelectedCount();
        }

        // 合并后的自动刷新逻辑
        setInterval(async () => {
            try {
                // 保存当前选中的学生名单
                const selectedStudents = Array.from(document.querySelectorAll('.student-card.selected'))
                    .map(card => card.getAttribute('data-name'));
                
                // 保存当前的考勤状态用于比较
                const oldAttendanceState = {};
                for (const name in studentDetails) {
                    if (studentDetails[name]) {
                        oldAttendanceState[name] = {
                            签到: studentDetails[name]['签到'],
                            签退: studentDetails[name]['签退']
                        };
                    }
                }
                
                // 加载新的考勤信息
                const { hasChanges, signInContent, signOutContent } = await loadAttendanceInfo();
                
                // 如果没有新数据，直接返回
                if (!hasChanges) {
                    return;
                }

                // 刷新请假信息
                await loadLeaveInfo();

                // 直接更新未签到视图中的卡片请假状态
                const unsignContainer = document.querySelector('.unsign-summary');
                if (unsignContainer) {
                    unsignContainer.querySelectorAll('.student-card').forEach(card => {
                        const name = card.getAttribute('data-name');
                        if (name) {
                            // 更新请假状态
                            if (leaveStudents.has(name)) {
                                card.classList.add('on-leave');
                                // 确保请假标记存在
                                if (!card.querySelector('.leave-badge')) {
                                    const leaveBadge = document.createElement('div');
                                    leaveBadge.className = 'leave-badge';
                                    leaveBadge.textContent = '假';
                                    card.appendChild(leaveBadge);
                                }
                            } else {
                                card.classList.remove('on-leave');
                                // 移除请假标记
                                const leaveBadge = card.querySelector('.leave-badge');
                                if (leaveBadge) {
                                    leaveBadge.remove();
                                }
                            }
                        }
                    });
                }
                
                // 更新年级视图中的卡片
                for (let grade = 1; grade <= 9; grade++) {
                    const container = document.getElementById(`grade-${grade}`);
                    if (container) {
                        container.querySelectorAll('.student-card').forEach(card => {
                            const name = card.getAttribute('data-name');
                            if (name) {
                                // 更新请假状态
                                if (leaveStudents.has(name)) {
                                    card.classList.add('on-leave');
                                    // 确保请假标记存在
                                    if (!card.querySelector('.leave-badge')) {
                                        const leaveBadge = document.createElement('div');
                                        leaveBadge.className = 'leave-badge';
                                        leaveBadge.textContent = '假';
                                        card.appendChild(leaveBadge);
                                    }
                                } else {
                                    card.classList.remove('on-leave');
                                    // 移除请假标记
                                    const leaveBadge = card.querySelector('.leave-badge');
                                    if (leaveBadge) {
                                        leaveBadge.remove();
                                    }
                                }
                            }
                        });
                    }
                }

                // 根据当前视图决定更新策略
                if (currentGrade === 'unsign') {
                    // 未签到视图 - 比较未签到学生列表
                    checkStudentListUpdate('sign');
                } else if (currentGrade === 'unsignout') {
                    // 未签退视图 - 比较未签退学生列表
                    checkStudentListUpdate('signout');
                } else if (currentGrade === 'teacher') {
                    // 教师视图 - 更新教师考勤
                    //await loadTeacherData();
                    updateTeacherStatusBarNew();
                } else {
                    // 年级视图 - 检查当前年级卡片状态变化
                    const container = document.getElementById(`grade-${currentGrade}`);
                    if (container) {
                        const cards = container.querySelectorAll('.student-card');
                        let hasChanges = false;
                        
                        for (const card of cards) {
                            const name = card.getAttribute('data-name');
                            const info = studentDetails[name] || {};
                            const isSigned = card.classList.contains('signed');
                            const currentSignIn = Boolean(info['签到']);
                            
                            // 比较卡片当前显示的签到状态和实际数据中的签到状态
                            if (currentSignIn !== isSigned) {
                                hasChanges = true;
                                // 记录发现的不一致
                                console.log(`发现卡片状态不一致: ${name} 卡片显示状态=${isSigned}, 实际数据状态=${currentSignIn}`);
                                break;
                            }

                            // 检查时间显示是否需要更新
                            const timeInfo = card.querySelector('.time-info');
                            if (timeInfo) {
                                const checkInElem = card.querySelector('.check-in-time');
                                const checkOutElem = card.querySelector('.check-out-time');
                                const currentCheckIn = checkInElem ? checkInElem.textContent : null;
                                const currentCheckOut = checkOutElem ? checkOutElem.textContent : null;
                                const expectedCheckIn = info['签到'] ? formatTimeForCard(info['签到']) : null;
                                const expectedCheckOut = info['签退'] ? formatTimeForCard(info['签退']) : null;
                                
                                // 检查签到和签退时间是否有变化
                                if (currentCheckIn !== expectedCheckIn || currentCheckOut !== expectedCheckOut) {
                                    hasChanges = true;
                                    break;
                                }
                            }
                        }
                        
                        if (hasChanges) {
                            console.log(`发现${currentGrade}年级卡片需要更新，开始更新...`);
                            updateAllCardsStatus();
                            updateStatusBar(currentGrade);
                            
                            // 恢复选中状态
                            selectedStudents.forEach(name => {
                                const card = container.querySelector(`.student-card[data-name="${name}"]`);
                                if (card) {
                                    card.classList.add('selected');
                                    const points = card.querySelector('.student-points');
                                    const infoButton = card.querySelector('.info-button');
                                    if (points) {
                                        points.style.opacity = '1';
                                        points.style.transform = 'translateY(0)';
                                    }
                                    if (infoButton) {
                                        infoButton.style.opacity = '1';
                                        infoButton.style.transform = 'translateY(0)';
                                    }
                                }
                            });
                        } else {
                            console.log('考勤状态无变化，保持现有显示');
                        }
                    }
                }
                
                // 全局检查所有考勤状态变化
                let hasGlobalChanges = false;
                for (const name in studentDetails) {
                    if (studentDetails[name] && oldAttendanceState[name]) {
                        if (studentDetails[name]['签到'] !== oldAttendanceState[name]['签到'] ||
                            studentDetails[name]['签退'] !== oldAttendanceState[name]['签退']) {
                            hasGlobalChanges = true;
                            break;
                        }
                    }
                }
                
                // 更新全局状态栏和所有年级卡片的时间显示
                if (hasGlobalChanges) {
                    // 更新状态栏（根据当前显示的视图）
                    if (currentGrade === 'teacher') {
                        updateTeacherStatusBarNew();
                    } else if (currentGrade === 'unsign' || currentGrade === 'unsignout') {
                        updateStatusBarAll();
                    } else {
                        updateStatusBar(currentGrade);
                    }
                    
                    // 更新所有年级的时间显示
                    updateDisplay();
                }
                
            } catch (error) {
                console.error('[自动刷新] 更新失败:', error);
            }
        }, 300000); // 修改为5分钟检查一次，作为WebSocket通知的备份机制

        // 新的数组比较函数
        function haveSameElements(arr1, arr2) {
            if (!Array.isArray(arr1) || !Array.isArray(arr2)) {
                return false;
            }
            
            if (arr1.length !== arr2.length) {
                return false;
            }
            
            // 创建两个集合进行比较
            const set1 = new Set(arr1);
            const set2 = new Set(arr2);
            
            // 检查每个集合中的元素是否都在另一个集合中
            for (const item of set1) {
                if (!set2.has(item)) {
                    return false;
                }
            }
            
            for (const item of set2) {
                if (!set1.has(item)) {
                    return false;
                }
            }
            
            return true;
        }

        // 获取所有未签到学生
        function getAllUnsignedStudents() {
            const unsignedStudents = [];
            for (let grade = 1; grade <= 9; grade++) {
                const students = studentsData[grade] || [];
                students.forEach(name => {
                    const signStatus = studentDetails[name]?.['签到'];
                    if (!signStatus) {
                        unsignedStudents.push(name);
                    }
                });
            }
            return unsignedStudents;
        }

        // 获取所有未签退学生（已签到但未签退）
        function getAllUnsignoutStudents() {
            const unsignoutStudents = [];
            for (let grade = 1; grade <= 9; grade++) {
                const students = studentsData[grade] || [];
                students.forEach(name => {
                    const info = studentDetails[name] || {};
                    const signinStatus = info['签到'];
                    const signoutStatus = info['签退'];
                    // 只有已签到但未签退的学生才算未签退
                    if (signinStatus && !signoutStatus) {
                        unsignoutStudents.push(name);
                    }
                });
            }
            return unsignoutStudents;
        }

        // 添加更新所有卡片状态的函数
        function updateAllCardsStatus() {
            for (let grade = 1; grade <= 9; grade++) {
                const container = document.getElementById(`grade-${grade}`);
                if (container) {
                    // 保存当前选中的学生
                    const selectedStudents = Array.from(container.querySelectorAll('.student-card.selected'))
                        .map(card => card.getAttribute('data-name'));

                    container.querySelectorAll('.student-card').forEach(card => {
                        const name = card.getAttribute('data-name');
                        const studentInfo = studentDetails[name] || {};
                        
                        // 更新签到状态，同时保持选中状态
                        const isSelected = selectedStudents.includes(name);
                        card.className = `student-card${studentInfo['签到'] ? ' signed' : ''}${leaveStudents.has(name) ? ' on-leave' : ''}${isSelected ? ' selected' : ''}`;
                        
                        // 更新时间显示
                        updateCardTime(card, studentInfo);

                        // 如果是选中状态，确保点数和信息按钮的显示状态
                        if (isSelected) {
                            const points = card.querySelector('.student-points');
                            const infoButton = card.querySelector('.info-button');
                            if (points) {
                                points.style.opacity = '1';
                                points.style.transform = 'translateY(0)';
                            }
                            if (infoButton) {
                                infoButton.style.opacity = '1';
                                infoButton.style.transform = 'translateY(0)';
                            }
                        }
                    });
                }
            }
            
            // 更新请假状态
            updateCardLeaveStatus();
            
            // 检查并更新未签到视图
            if (currentGrade === 'unsign') {
                checkStudentListUpdate('sign');
            }
            
            // 检查并更新未签退视图
            if (currentGrade === 'unsignout') {
                checkStudentListUpdate('signout');
            }
        }

        // 加载拼音数据
        async function loadPinyinData() {
            try {
                // 修改后：使用API获取拼音数据
                const response = await fetch('/api/pinyin');
                if (!response.ok) {
                    throw new Error('获取拼音数据失败');
                }
                const text = await response.text();
                
                // 清空现有数据
                nameToPinyin = {};
                
                // 解析INI格式
                let currentPinyin = '';
                const lines = text.split('\n');
                
                lines.forEach(line => {
                    line = line.trim();
                    if (!line) return;
                    
                    // 移除可能的行号和竖线
                    line = line.replace(/^\d+\|\s*/, '');
                    
                    if (line.startsWith('[') && line.endsWith(']')) {
                        currentPinyin = line.slice(1, -1);
                    } else if (line.startsWith('Name=')) {
                        const name = line.substring(5);
                        if (currentPinyin && name) {
                            if (name.includes('|')) {
                                name.split('|').forEach(n => {
                                    nameToPinyin[n.trim()] = currentPinyin;
                                });
                            } else {
                                nameToPinyin[name] = currentPinyin;
                            }
                        }
                    }
                });
                
                if (Object.keys(nameToPinyin).length === 0) {
                    throw new Error('未能解析到任何拼音数据');
                }

            } catch (error) {
                throw new Error('加载拼音数据失败：' + error.message);
            }
        }

        // 加载学分信息
        async function loadStudentPoints() {
            try {
                const response = await fetch('/api/student-points');
                if (!response.ok) {
                    throw new Error('获取学分数据失败');
                }
                const text = await response.text();
                
                let currentName = '';
                const lines = text.split('\n');

                for (let line of lines) {
                    line = line.trim();
                    if (line.startsWith('[') && line.endsWith(']')) {
                        currentName = line.slice(1, -1);
                    } else if (line.startsWith('学分=')) {
                        const score = parseInt(line.split('=')[1]);
                        if (currentName && !isNaN(score)) {
                            studentPoints[currentName] = score;
                        }
                    }
                }

                if (Object.keys(studentPoints).length === 0) {
                    throw new Error('未能解析到任何学分数据');
                }
                
            } catch (error) {
                throw new Error('加载学分信息失败：' + error.message);
            }
        }

        // 加载考勤信息
        async function loadAttendanceInfo() {
            try {
                // 保存当前的考勤状态用于比较
                const oldState = {};
                Object.keys(studentDetails).forEach(name => {
                    if (studentDetails[name]) {
                        oldState[name] = {
                            签到: studentDetails[name]['签到'] || null,
                            签退: studentDetails[name]['签退'] || null
                        };
                    }
                });

                // 临时存储新的签到签退状态
                const newState = {};
                
                // 临时存储从签到签退文件中解析出的学生姓名
                const allStudentNames = new Set();
                
                // 【修改开始】 将 signInContent 和 signOutContent 声明提升到外部作用域
                let signInContent = '';
                let signOutContent = '';
                // 【修改结束】

                // 初始化所有已有学生的签到签退状态为null（重要：这确保学生记录被删除时也能检测到）
                Object.keys(studentDetails).forEach(name => {
                    newState[name] = { 签到: null, 签退: null };
                });

                // 加载签到信息
                    try {
                        const signInResponse = await fetch('/api/attendance?type=签到');
                        if (!signInResponse.ok) {
                            throw new Error('获取签到文件失败');
                        }
                        // 【修改开始】 赋值给外部变量
                        signInContent = await signInResponse.text();
                        // 【修改结束】
                        
                        const lines = signInContent.split('\n');
                        let currentName = '';
                        
                        for (let line of lines) {
                            line = line.trim();
                            if (line.startsWith('[') && line.endsWith(']')) {
                                currentName = line.slice(1, -1);
                                allStudentNames.add(currentName);
                                
                                // 确保该学生在 newState 中有记录
                                if (!newState[currentName]) {
                                    newState[currentName] = { 签到: null, 签退: null };
                                }
                            } else if (line.startsWith('首次签到时间=') && currentName) {
                                const timeStr = line.split('=')[1]?.trim();
                                if (timeStr && typeof timeStr === 'string') {
                                    if (!newState[currentName]) {
                                        newState[currentName] = { 签到: null, 签退: null };
                                    }
                                    newState[currentName]['签到'] = timeStr;
                                }
                            }
                        }
                    } catch (error) {
                        console.log('加载签到文件失败:', error);
                        // 继续执行，不中断流程
                    }

                // 加载签退信息
                    try {
                        const signOutResponse = await fetch('/api/attendance?type=签退');
                        if (!signOutResponse.ok) {
                            throw new Error('获取签退文件失败');
                        }
                        // 【修改开始】 赋值给外部变量
                        signOutContent = await signOutResponse.text();
                        // 【修改结束】
                        
                        const lines = signOutContent.split('\n');
                        let currentName = '';
                        
                        for (let line of lines) {
                            line = line.trim();
                            if (line.startsWith('[') && line.endsWith(']')) {
                                currentName = line.slice(1, -1);
                                allStudentNames.add(currentName);
                                
                                // 确保该学生在 newState 中有记录
                                if (!newState[currentName]) {
                                    newState[currentName] = { 签到: null, 签退: null };
                                }
                            } else if (line.startsWith('首次签退时间=') && currentName) {
                                const timeStr = line.split('=')[1]?.trim();
                                if (timeStr && typeof timeStr === 'string') {
                                    if (!newState[currentName]) {
                                        newState[currentName] = { 签到: null, 签退: null };
                                    }
                                    newState[currentName]['签退'] = timeStr;
                                }
                            }
                        }
                    } catch (error) {
                        console.log('加载签退文件失败:', error);
                        // 继续执行，不中断流程
                    }

                // 确保所有从签到签退文件中解析出的学生在 studentDetails 中有记录
                allStudentNames.forEach(name => {
                    if (!studentDetails[name]) {
                        // console.log(`为签到/签退学生 ${name} 创建基本信息记录`);
                        studentDetails[name] = {
                            '学校': '',
                            '年级': '',
                            '班级': '',
                            '电话': [],
                            '住址': '',
                            '备注': ''
                        };
                    }
                });

                // 检查是否有变化并更新状态
                let hasChanges = false;
                
                // 处理所有学生，包括那些在考勤文件中被删除的记录
                Object.keys(studentDetails).forEach(name => {
                    const oldSignIn = oldState[name]?.['签到'] || null;
                    const oldSignOut = oldState[name]?.['签退'] || null;
                    const newSignIn = newState[name]?.['签到'] || null;
                    const newSignOut = newState[name]?.['签退'] || null;

                    // 检查签到或签退状态是否有变化
                    if (oldSignIn !== newSignIn || oldSignOut !== newSignOut) {
                        hasChanges = true;
                        
                        // 更新学生的签到签退信息
                        if (!studentDetails[name]) {
                            studentDetails[name] = {
                                '学校': '',
                                '年级': '',
                                '班级': '',
                                '电话': [],
                                '住址': '',
                                '备注': ''
                            };
                        }
                        studentDetails[name]['签到'] = newSignIn;
                        studentDetails[name]['签退'] = newSignOut;
                        
                        // 记录签到签退状态变化
                        // console.log(`学生 ${name} 签到状态变更: ${oldSignIn} => ${newSignIn}, 签退状态变更: ${oldSignOut} => ${newSignOut}`);
                    }
                });

                // 【修改开始】 返回一个对象，包含 hasChanges 和原始文件内容
                return {
                    hasChanges: hasChanges,
                    signInContent: signInContent,
                    signOutContent: signOutContent
                };
                // 【修改结束】
            } catch (error) {
                console.log('考勤信息加载失败，但将继续执行其他功能:', error);
                // 【修改开始】 即使出错也返回空字符串，避免 loadTeacherData 收到 undefined
                return {
                    hasChanges: false,
                    signInContent: '',
                    signOutContent: ''
                };
                // 【修改结束】
            }
        }

        // 简化后的请假信息加载函数
        async function loadLeaveInfo() {
            try {
                // 直接调用修改后的API，后端会自动从签到文件中获取日期
                const response = await fetch('/api/leave-records');
                
                if (!response.ok) {
                    console.error('[loadLeaveInfo] 获取请假信息失败:', response.status);
                    // 如果API请求失败，清空请假学生数据并退出
                    leaveStudents.clear();
                    return;
                }
                
                const text = await response.text();
                const lines = text.split('\n');
                
                leaveStudents.clear();  // 清空现有数据
                lines.forEach(line => {
                    const lineContent = line.trim();
                    if (lineContent) {
                        // 遍历所有学生，检查他们的名字是否包含在这一行中
                        Object.values(studentsData).flat().forEach(studentName => {
                            if (lineContent.includes(studentName)) {
                                leaveStudents.add(studentName);
                            }
                        });
                    }
                });

            } catch (error) {
                console.error('[loadLeaveInfo] 加载请假信息失败:', error);
                // 错误处理，但不中断应用流程
            }
        }

        // 修改格式化时间的函数
        function formatTime(timeStr, showSeconds = true) {
            if (!timeStr || timeStr === '--:--' || timeStr === true) return showSeconds ? '--:--:--' : '--:--';
            
            // 如果是布尔值true（强制签到/签退的情况），返回当前时间
            if (typeof timeStr === 'boolean') {
                const now = new Date();
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                return showSeconds ? 
                    `${hours}:${minutes}:${String(now.getSeconds()).padStart(2, '0')}` : 
                    `${hours}:${minutes}`;
            }
            
            // 转为字符串确保处理安全
            const timeString = String(timeStr);
            
            // 处理 HH:MM:SS 格式的时间字符串
            if (timeString.match(/^\d{1,2}:\d{1,2}(:\d{1,2})?$/)) {
                // 已经是冒号分隔的格式，直接检查是否需要显示秒
                const parts = timeString.split(':');
                if (parts.length === 3 && showSeconds) {
                    // 已经有秒，直接返回
                    return `${parts[0].padStart(2, '0')}:${parts[1].padStart(2, '0')}:${parts[2].padStart(2, '0')}`;
                } else if (parts.length === 2) {
                    // 只有时和分
                    return showSeconds ? 
                        `${parts[0].padStart(2, '0')}:${parts[1].padStart(2, '0')}:00` : 
                        `${parts[0].padStart(2, '0')}:${parts[1].padStart(2, '0')}`;
                }
                return timeString; // 其他情况直接返回原字符串
            }
            
            // 处理 "XX点XX分XX秒" 格式
            const match = timeString.match(/(\d+)点(\d+)分(\d+)秒/);
            if (match) {
                const [_, hours, minutes, seconds] = match;
                return showSeconds ? 
                    `${hours.padStart(2, '0')}:${minutes.padStart(2, '0')}:${seconds.padStart(2, '0')}` : 
                    `${hours.padStart(2, '0')}:${minutes.padStart(2, '0')}`;
            }
            
            // 如果都不匹配，返回原始格式或默认值
            return showSeconds ? timeString : timeString.split(':').slice(0, 2).join(':');
        }

        // 绑定卡片事件
        function bindCardEvents(card, name) {
            // 为整个卡片添加点击事件
            card.addEventListener('click', (e) => {
                // 如果点击的是信息按钮，不触发任何效果
                if (e.target.closest('.info-button')) {
                    return;
                }
                
                // 如果点击的是积分区域，打开签到记录查询
                if (e.target.closest('.student-points')) {
                    e.stopPropagation(); // 阻止冒泡，避免触发卡片选中
                    
                    // 隐藏学生信息模态框(如果打开的话)
                    const studentModal = document.getElementById('studentModal');
                    if (studentModal && studentModal.style.display === 'block') {
                        studentModal.style.display = 'none';
                    }
                    
                    // 显示签到记录模态框
                    const attendanceModal = document.getElementById('attendanceModal');
                    if (attendanceModal) {
                        // 显示模态框
                        attendanceModal.style.display = 'block';
                        
                        // 初始化查询界面
                        if (window.AttendanceQuery) {
                            window.AttendanceQuery.initAttendanceQuery();
                            
                            // 自动填入学生姓名并执行查询
                            const studentNameInput = document.getElementById('student-name-input');
                            if (studentNameInput) {
                                studentNameInput.value = name;
                                
                                // 执行查询
                                //window.AttendanceQuery.queryAttendanceRecord(name);
                            }
                        }
                        
                        // 绑定关闭事件
                        const closeButton = attendanceModal.querySelector('.close-modal');
                        if (closeButton) {
                            closeButton.onclick = function() {
                                attendanceModal.style.display = 'none';
                            };
                        }
                    }
                    return;
                }

                // 切换选中状态
                toggleCardSelection(card);

                // 暂时注释掉发送消息的代码
                // sendMessage('cardClick', `点击了学生: ${name}`);

                // 如果点击的是头像区域，播放音频
                if (e.target.closest('.student-avatar')) {
                    playAudio(name);
                }
            });
            
            // 为信息按钮单独添加点击事件
            const infoButton = card.querySelector('.info-button');
            if (infoButton) {
                infoButton.addEventListener('click', (e) => {
                    e.stopPropagation();  // 阻止事件冒泡
                    showStudentDetails(name);
                });
            }
        }

        // 修改发送消息的函数
        async function sendMessage(type, message) {
            const messageData = {
                type: type,
                message: message,
                timestamp: new Date().toLocaleString('zh-CN', { hour12: false })
            };

            try {
                await sendMessageWithTimeout(messageData);
            } catch (error) {
                // 如果发送失败，将消息加入队列
                MESSAGE_QUEUE.push({
                    message: messageData,
                    resolve: () => {},
                    reject: () => {}
                });
            }
        }

        // 修改卡片选中状态切换函数
        function toggleCardSelection(card) {
            const wasSelected = card.classList.contains('selected');
                card.classList.toggle('selected');
            
            // 获取积分和信息按钮元素
            const points = card.querySelector('.student-points');
            const infoButton = card.querySelector('.info-button');
            
            // 根据选中状态设置透明度和变换
            if (!wasSelected) {
                // 选中时显示
                if (points) {
                    points.style.opacity = '1';
                    points.style.transform = 'translateY(0)';
                }
                if (infoButton) {
                    infoButton.style.opacity = '1';
                    infoButton.style.transform = 'translateY(0)';
                }
            } else {
                // 取消选中时隐藏
                if (points) {
                    points.style.opacity = '0';
                    points.style.transform = 'translateY(-10px)';
                }
                if (infoButton) {
                    infoButton.style.opacity = '0';
                    infoButton.style.transform = 'translateY(-10px)';
                }
            }

            // 更新选中数量显示
            updateSelectedCount();

            // 更新按钮文字
            const gradeSection = card.closest('.grade-section');
            if (gradeSection) {
                const type = document.querySelector('#grade-unsignout').contains(card) ? 'signout' : 'sign';
                const grade = Array.from(gradeSection.parentElement.children).indexOf(gradeSection) + 1;
                updateBatchSignButton(grade, type);
            }
        }

        // 添加更新选中数量的函数
        function updateSelectedCount() {
            const selectedCards = document.querySelectorAll('.student-card.selected');
            const selectedCountElement = document.getElementById('selectedCount');
            
            if (selectedCards.length > 0) {
                selectedCountElement.textContent = `已选中 ${selectedCards.length} 人`;
                selectedCountElement.style.opacity = '1';
            } else {
                selectedCountElement.style.opacity = '0';
            }
        }

        // 显示学生详细信息
        function showStudentDetails(studentName) {
            const modal = document.getElementById('studentModal');
            const modalAvatar = document.getElementById('modalStudentAvatar');
            const modalName = document.getElementById('modalStudentName');
            const modalPinyin = document.getElementById('modalPinyin');
            const modalSchool = document.getElementById('modalSchool');
            const modalGrade = document.getElementById('modalGrade');
            const modalClass = document.getElementById('modalClass');
            const modalAddress = document.getElementById('modalAddress');
            const modalRemark = document.getElementById('modalRemark');
            const locationLink = modal.querySelector('.location-link');
            const phoneList = document.getElementById('phoneList');
            const modalSignInTime = document.getElementById('modalSignInTime');
            const modalSignOutTime = document.getElementById('modalSignOutTime');
            const deleteStudentBtn = document.getElementById('deleteStudentBtn');
            
            // 只在管理员登录时显示删除按钮
            if (localStorage.getItem('currentUser') === 'ling') {
                deleteStudentBtn.style.display = 'inline-block';
            } else {
                deleteStudentBtn.style.display = 'none';
            }

            // 设置模态框关闭事件
            const closeBtn = modal.querySelector('.close-modal');
            if (closeBtn) {
                closeBtn.onclick = () => {
                    modal.style.display = 'none';
                    document.querySelector('.search-button').classList.remove('active');
                };
            }

            // 点击模态框外部关闭
            modal.onclick = function(event) {
                if (event.target === modal) {
                    modal.style.display = 'none';
                    document.querySelector('.search-button').classList.remove('active');
                }
            };

            const studentInfo = studentDetails[studentName] || {};
            const pinyin = nameToPinyin[studentName] || '未设置';
            const phones = studentInfo['电话'] || [];

            // 设置签到签退时间
            updateTimeStatus(modalSignInTime, studentInfo['签到'], '签到');
            updateTimeStatus(modalSignOutTime, studentInfo['签退'], '签退');

            // 先尝试从已加载的学生卡片中查找头像
            let existingAvatar = null;

            // 在所有年级容器中查找该学生的卡片和头像
            for (let grade = 1; grade <= 9; grade++) {
                const gradeContainer = document.getElementById(`grade-${grade}`);
                if (gradeContainer) {
                    const card = gradeContainer.querySelector(`.student-card[data-name="${studentName}"]`);
                    if (card) {
                        const avatar = card.querySelector('.student-avatar img');
                        if (avatar && avatar.complete && avatar.naturalHeight !== 0 && avatar.src && !avatar.src.includes('默认头像.png')) {
                            existingAvatar = avatar;
                            break;
                        }
                    }
                }
            }

            // 在未签到和未签退汇总页面也查找
            if (!existingAvatar) {
                ['unsign', 'unsignout'].forEach(type => {
                    const container = document.getElementById(`grade-${type}`);
                    if (container) {
                        const card = container.querySelector(`.student-card[data-name="${studentName}"]`);
                        if (card) {
                            const avatar = card.querySelector('.student-avatar img');
                            if (avatar && avatar.complete && avatar.naturalHeight !== 0 && avatar.src && !avatar.src.includes('默认头像.png')) {
                                existingAvatar = avatar;
                            }
                        }
                    }
                });
            }

            // 如果找到已加载的头像，直接使用它
            if (existingAvatar) {
                modalAvatar.src = existingAvatar.src;
                modalAvatar.onload = () => {
                    modalAvatar.classList.remove('default-avatar');
                };
            } else {
                // 否则从服务器加载头像
                modalAvatar.src = `./FaceID/学生照片/${pinyin}.jpg?t=${new Date().getTime()}`;
            }

            modalAvatar.onerror = () => {
                modalAvatar.src = './node_modules/默认头像.png';
                modalAvatar.classList.add('default-avatar');
            };
            
            // 修改头像点击事件，所有头像都可点击
            modalAvatar.onclick = function() {
                const pinyin = modalPinyin.textContent;
                if (pinyin === '未设置') {
                    showMessage('请先设置学生工号', 'error');
                    return;
                }

                // 创建文件输入框
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = 'image/*';  // 接受所有图片格式
                input.style.display = 'none';
                
                // 添加文件选择事件
                input.onchange = async function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        await handlePhotoUpload(file, studentName, pinyin);
                    }
                    // 移除临时创建的input元素
                    input.remove();
                };
                
                // 触发文件选择
                document.body.appendChild(input);
                input.click();
            };
            
            modalName.textContent = studentName;
            modalPinyin.textContent = pinyin;
            
            // 设置学校
            modalSchool.value = studentInfo['学校'] || '';
            
            // 设置年级：优先使用已保存的年级信息，否则从studentsData中查找实际所在年级
            let studentGrade = '';
            if (studentInfo['年级']) {
                studentGrade = studentInfo['年级'].replace('年级', '');
                modalGrade.value = studentInfo['年级'];
            } else {
                for (let grade = 1; grade <= 9; grade++) {
                    if (studentsData[grade] && studentsData[grade].includes(studentName)) {
                        studentGrade = grade;
                        break;
                    }
                }
                modalGrade.value = studentGrade ? `${studentGrade}年级` : '';
            }
            // 保存当前学生年级和姓名到全局变量
            currentStudentGrade = studentGrade;
            currentStudentName = studentName;
            
            // 设置班级
            modalClass.value = studentInfo['班级'] || '';
            
            // 设置电话号码列表
            phoneList.innerHTML = '';
            if (phones.length > 0) {
                phones.forEach((phone, index) => {
                    addPhoneNumberToList(phone);
                });
                    } else {
                addPhoneNumberToList(''); // 添加一个空的电话输入框
            }
            
            // 设置地址
            modalAddress.value = studentInfo['住址'] || '';
            
            // 设置备注
            modalRemark.value = studentInfo['备注'] || '';

            // 设置地图链接
            updateLocationLink();

            modal.style.display = 'block';
            
            // 绑定保存事件
            setupSaveEvents();

            // 设置签到记录按钮事件
            setupAttendanceRecordButton(studentName);
        }

        // 设置签到记录按钮事件
        function setupAttendanceRecordButton(studentName) {
            const attendanceButton = document.getElementById('showAttendanceRecords');
            if (attendanceButton) {
                attendanceButton.onclick = function() {
                    // 隐藏学生信息模态框
                    const studentModal = document.getElementById('studentModal');
                    if (studentModal) {
                        studentModal.style.display = 'none';
                    }
                    
                    // 显示签到记录模态框
                    const attendanceModal = document.getElementById('attendanceModal');
                    if (attendanceModal) {
                        attendanceModal.style.display = 'block';

                        // 绑定关闭事件
                        const closeButton = attendanceModal.querySelector('.close-modal');
                        if (closeButton) {
                            closeButton.onclick = function() {
                                attendanceModal.style.display = 'none';
                            };
                        }
                    }

                    // 获取学生姓名
                    const studentNameForQuery = studentName || document.getElementById('modalStudentName').textContent;
                    
                    // 调用辅助函数来切换到正确的标签页并执行查询
                    // 这是修复问题的关键，确保了签到查询的UI和逻辑被正确触发
                    if (window.AttendanceQuery && window.AttendanceQuery.switchToAttendanceQueryTab) {
                        window.AttendanceQuery.switchToAttendanceQueryTab(studentNameForQuery);
                    }
                };
            }
        }

        // 添加新的电话号码输入行
        function addPhoneNumber() {
            addPhoneNumberToList('');
        }

        // 添加电话号码到列表
        function addPhoneNumberToList(phoneNumber) {
            const phoneList = document.getElementById('phoneList');
            const phoneDiv = document.createElement('div');
            phoneDiv.className = 'phone-number';
            phoneDiv.innerHTML = `
                <input type="tel" value="${phoneNumber}" pattern="[0-9]*" maxlength="11">
                <a class="icon-link phone-link" title="拨打电话">
                    <svg viewBox="0 0 24 24">
                        <path fill="currentColor" d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/>
                    </svg>
                </a>
                <button class="phone-action-btn phone-delete-btn" onclick="deletePhoneNumber(this)" title="删除电话">
                    -
                </button>
            `;
            
            // 绑定电话号码输入事件
            const input = phoneDiv.querySelector('input');
            input.addEventListener('input', function(e) {
                this.value = this.value.replace(/[^0-9]/g, '');
            });
            
            // 绑定拨打电话事件
            const phoneLink = phoneDiv.querySelector('.phone-link');
            phoneLink.onclick = () => {
                const number = input.value;
                if (number) {
                    window.open(`tel:${number}`);
                }
            };
            
            phoneList.appendChild(phoneDiv);
        }

        // 删除电话号码
        function deletePhoneNumber(button) {
            const phoneDiv = button.parentElement;
            const phoneList = phoneDiv.parentElement;
            phoneList.removeChild(phoneDiv);
        }

        // 更新地图链接
        function updateLocationLink() {
            const address = document.getElementById('modalAddress').value;
            const locationLink = document.querySelector('.location-link');
            
            if (address && address !== '未知') {
                locationLink.onclick = () => {
                    window.open(`https://map.baidu.com/?newmap=1&ie=utf-8&from=pstab&s=s%26wd%3D${encodeURIComponent(address)}`, '_blank');
                };
                locationLink.style.display = 'inline-flex';
            } else {
                locationLink.style.display = 'none';
            }
        }

        // 设置保存事件
        function setupSaveEvents() {
            const modalAddress = document.getElementById('modalAddress');
            modalAddress.addEventListener('input', updateLocationLink);
            
            // 限制班级输入
            const modalClass = document.getElementById('modalClass');
            modalClass.addEventListener('input', function(e) {
                this.value = this.value.replace(/[^0-9]/g, '');
                if (this.value > 99) this.value = 99;
                if (this.value < 0) this.value = 0;
            });
        }

        // 保存学生信息
        async function saveStudentInfo() {
            const studentName = document.getElementById('modalStudentName').textContent;
            const modalSchool = document.getElementById('modalSchool');
            const modalGrade = document.getElementById('modalGrade');
            const modalClass = document.getElementById('modalClass');
            const modalAddress = document.getElementById('modalAddress');
            const modalRemark = document.getElementById('modalRemark');
            const phoneInputs = document.querySelectorAll('#phoneList input[type="tel"]');
            const searchButton = document.querySelector('.search-button');

            // 收集电话号码
            const phones = Array.from(phoneInputs)
                .map(input => input.value)
                .filter(phone => phone.trim() !== '');

            // 更新学生信息
            if (!studentDetails[studentName]) {
                studentDetails[studentName] = {};
            }

            // 检查年级格式
            let gradeValue = modalGrade.value.trim();
            if (!gradeValue.endsWith('年级')) {
                gradeValue = gradeValue + '年级';
            }

            studentDetails[studentName]['学校'] = modalSchool.value;
            studentDetails[studentName]['年级'] = gradeValue;
            studentDetails[studentName]['班级'] = modalClass.value;
            studentDetails[studentName]['电话'] = phones;
            studentDetails[studentName]['住址'] = modalAddress.value;
            studentDetails[studentName]['备注'] = modalRemark.value;

            try {
                // 只发送当前修改的学生信息到服务器，而不是整个studentDetails对象
                const singleStudentData = {
                    [studentName]: studentDetails[studentName]
                };
                
                // 发送保存请求到服务器
                const response = await fetch('/save-student-info', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(singleStudentData)
                });

                if (!response.ok) {
                    throw new Error('保存失败');
                }

                // 显示保存成功提示
                //showMessage('保存成功', 'success');
                showToast('学员信息保存成功！');
                
                // 等待提示消失后再关闭模态框和更新显示
                setTimeout(() => {
                    document.getElementById('studentModal').style.display = 'none';
                    searchButton.classList.remove('active');
                    updateAllCardsStatus();
                }, 2300);  // 比提示显示时间稍长一点
                
            } catch (error) {
                console.error('保存失败:', error);
                showMessage('保存失败，请重试', 'error');
            }
        }

        // 搜索同班、同学校、同小区学生
        async function searchClassmates(type) {
            const studentName = document.getElementById('modalStudentName').textContent;
            if (!studentName) {
                showMessage('无法获取学生姓名', 'error');
                return;
            }

            // 根据搜索类型验证必要字段
            const modalSchool = document.getElementById('modalSchool');
            const modalGrade = document.getElementById('modalGrade');
            const modalClass = document.getElementById('modalClass');
            const modalAddress = document.getElementById('modalAddress');

            if (type === 'class') {
                // 同班搜索需要学校、年级、班级都不为空
                if (!modalSchool.value.trim()) {
                    showMessage('学校不能为空，无法搜索同班学生', 'error');
                    modalSchool.focus();
                    return;
                }
                if (!modalGrade.value.trim()) {
                    showMessage('年级不能为空，无法搜索同班学生', 'error');
                    modalGrade.focus();
                    return;
                }
                if (!modalClass.value.trim()) {
                    showMessage('班级不能为空，无法搜索同班学生', 'error');
                    modalClass.focus();
                    return;
                }
            } else if (type === 'grade') {
                // 同年级搜索需要学校、年级不为空
                if (!modalSchool.value.trim()) {
                    showMessage('学校不能为空，无法搜索同年级学生', 'error');
                    modalSchool.focus();
                    return;
                }
                if (!modalGrade.value.trim()) {
                    showMessage('年级不能为空，无法搜索同年级学生', 'error');
                    modalGrade.focus();
                    return;
                }
            } else if (type === 'school') {
                // 同校搜索需要学校不为空
                if (!modalSchool.value.trim()) {
                    showMessage('学校不能为空，无法搜索同学校学生', 'error');
                    modalSchool.focus();
                    return;
                }
            } else if (type === 'address') {
                // 同区搜索需要住址不为空
                if (!modalAddress.value.trim()) {
                    showMessage('住址不能为空，无法搜索同小区学生', 'error');
                    modalAddress.focus();
                    return;
                }
            }

            try {
                // 显示加载状态
                showToast('正在搜索...');

                // 调用API搜索同班同学
                const response = await fetch(`/api/search-classmates?name=${encodeURIComponent(studentName)}`);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.message || '搜索失败');
                }

                if (!data.success) {
                    showMessage(data.message || '搜索失败', 'error');
                    return;
                }

                // 显示搜索结果
                showClassmatesResults(data, type);

            } catch (error) {
                console.error('搜索同班同学失败:', error);
                showMessage('搜索失败，请重试', 'error');
            }
        }

        // 显示搜索结果
        function showClassmatesResults(data, activeTab = 'classmates') {
            const modal = document.getElementById('classmatesModal');
            const title = document.getElementById('classmatesModalTitle');

            // 设置标题
            title.textContent = `${data.targetStudent.name} 的同学搜索结果`;

            // 更新计数
            document.getElementById('classmatesCount').textContent = data.classmates.length;
            document.getElementById('gradematesCount').textContent = data.grademates.length;
            document.getElementById('schoolmatesCount').textContent = data.schoolmates.length;
            document.getElementById('neighborsCount').textContent = data.neighbors.length;

            // 渲染各个列表
            renderClassmatesList('classmatesList', data.classmates, '同班学生');
            renderClassmatesList('gradematesList', data.grademates, '同年级学生');
            renderClassmatesList('schoolmatesList', data.schoolmates, '同学校学生');
            renderClassmatesList('neighborsList', data.neighbors, '同小区学生');

            // 根据搜索类型设置默认激活的标签页
            let defaultTab = 'classmates';
            if (activeTab === 'grade') {
                defaultTab = 'grademates';
            } else if (activeTab === 'school') {
                defaultTab = 'schoolmates';
            } else if (activeTab === 'address') {
                defaultTab = 'neighbors';
            }

            // 设置标签页
            setupClassmatesTabs(defaultTab);

            // 显示模态框
            modal.style.display = 'block';
        }

        // 渲染同学列表
        function renderClassmatesList(containerId, students, emptyMessage) {
            const container = document.getElementById(containerId);

            if (students.length === 0) {
                container.innerHTML = `
                    <div class="empty-result">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 21H5V3H13V9H19Z"/>
                        </svg>
                        <div>暂无${emptyMessage}</div>
                    </div>
                `;
                return;
            }

            // 对学生列表进行排序：在读学生优先，已毕业学生按毕业年份倒序排列
            const sortedStudents = [...students].sort((a, b) => {
                // 提取毕业信息
                function getGraduationInfo(notes) {
                    if (!notes || !notes.includes('毕业')) {
                        return { isGraduated: false, year: 0 };
                    }

                    // 匹配年份+已毕业的模式
                    const yearMatch = notes.match(/(\d{4})已毕业/);
                    if (yearMatch) {
                        return { isGraduated: true, year: parseInt(yearMatch[1]) };
                    }

                    // 只有"已毕业"没有年份，视为最早毕业（年份为0）
                    if (notes.includes('已毕业')) {
                        return { isGraduated: true, year: 0 };
                    }

                    return { isGraduated: false, year: 0 };
                }

                const aGradInfo = getGraduationInfo(a.info.notes || '');
                const bGradInfo = getGraduationInfo(b.info.notes || '');

                // 在读学生优先显示
                if (!aGradInfo.isGraduated && bGradInfo.isGraduated) return -1;
                if (aGradInfo.isGraduated && !bGradInfo.isGraduated) return 1;

                // 如果都是在读学生，按姓名排序
                if (!aGradInfo.isGraduated && !bGradInfo.isGraduated) {
                    return a.name.localeCompare(b.name, 'zh-CN');
                }

                // 如果都是已毕业学生，按毕业年份倒序排列（最新毕业的在前）
                if (aGradInfo.isGraduated && bGradInfo.isGraduated) {
                    // 特殊处理：年份为0（无年份的"已毕业"）排在最后
                    if (aGradInfo.year === 0 && bGradInfo.year !== 0) return 1;
                    if (aGradInfo.year !== 0 && bGradInfo.year === 0) return -1;
                    if (aGradInfo.year === 0 && bGradInfo.year === 0) {
                        return a.name.localeCompare(b.name, 'zh-CN');
                    }

                    // 年份不同时，年份大的在前（2025 > 2024 > 2023）
                    if (aGradInfo.year !== bGradInfo.year) {
                        return bGradInfo.year - aGradInfo.year;
                    }
                    // 年份相同时，按姓名排序
                    return a.name.localeCompare(b.name, 'zh-CN');
                }

                return 0;
            });

            // 创建一个临时存储对象来保存学生信息
            window.tempStudentInfoCache = window.tempStudentInfoCache || {};

            container.innerHTML = sortedStudents.map((student, index) => {
                // 构建学校班级信息
                const schoolInfo = [
                    student.info.school || '',
                    student.info.grade || '',
                    student.info.class ? `${student.info.class}班` : ''
                ].filter(item => item).join(' ');

                // 构建住址信息
                const addressInfo = student.info.address || '';

                // 构建备注信息
                const notesInfo = student.info.notes || '';

                // 根据备注内容确定样式类
                let notesClass = 'classmate-notes';
                if (notesInfo.includes('毕业')) {
                    notesClass += ' graduated';
                } else if (notesInfo.includes('在读') || notesInfo.includes('优秀')) {
                    notesClass += ' active';
                } else if (notesInfo.includes('转学') || notesInfo.includes('退学')) {
                    notesClass += ' transferred';
                }

                // 使用安全的方式存储学生信息
                const cacheKey = `${containerId}_${index}_${student.name}`;
                window.tempStudentInfoCache[cacheKey] = student.info;

                return `
                <div class="classmate-item" onclick="showStudentFromSearchSafe('${student.name}', '${cacheKey}')">
                    <img class="classmate-avatar"
                         src="./FaceID/学生照片/${nameToPinyin[student.name] || ''}.jpg?t=${new Date().getTime()}"
                         alt="${student.name}"
                         onerror="this.src='./node_modules/默认头像.png'">
                    <div class="classmate-info">
                        <div class="classmate-name">${student.name}</div>
                        ${schoolInfo ? `<div class="classmate-school-info">${schoolInfo}</div>` : ''}
                        ${addressInfo ? `<div class="classmate-address">${addressInfo}</div>` : ''}
                    </div>
                    ${notesInfo ? `<div class="${notesClass}">${notesInfo}</div>` : ''}
                </div>
                `;
            }).join('');
        }

        // 标记是否已经绑定过事件
        let classmatesTabsInitialized = false;

        // 设置标签页功能
        function setupClassmatesTabs(activeTab = 'classmates') {
            const tabs = document.querySelectorAll('.classmates-tab');
            const contents = document.querySelectorAll('.classmates-tab-content');

            console.log('设置标签页:', activeTab);
            console.log('找到标签页数量:', tabs.length);

            // 移除所有激活状态
            tabs.forEach(tab => {
                tab.classList.remove('active');
                // 移除内联样式，确保CSS类能正常工作
                tab.style.background = '';
                tab.style.color = '';
            });
            contents.forEach(content => {
                content.classList.remove('active');
                content.style.display = 'none';
            });

            // 设置默认激活的标签页
            const activeTabElement = document.querySelector(`[data-tab="${activeTab}"]`);
            const activeContent = document.getElementById(`${activeTab}Content`);

            console.log('激活的标签页元素:', activeTabElement);
            console.log('激活的内容元素:', activeContent);

            if (activeTabElement && activeContent) {
                activeTabElement.classList.add('active');
                activeContent.classList.add('active');
                activeContent.style.display = 'block';
                console.log('成功激活标签页:', activeTab);
            }

            // 只在第一次时绑定标签页点击事件
            if (!classmatesTabsInitialized) {
                console.log('绑定标签页点击事件');
                tabs.forEach(tab => {
                    tab.addEventListener('click', function() {
                        const tabName = this.getAttribute('data-tab');
                        console.log('点击标签页:', tabName);

                        // 移除所有激活状态
                        document.querySelectorAll('.classmates-tab').forEach(t => {
                            t.classList.remove('active');
                            // 移除内联样式
                            t.style.background = '';
                            t.style.color = '';
                        });
                        document.querySelectorAll('.classmates-tab-content').forEach(c => {
                            c.classList.remove('active');
                            c.style.display = 'none';
                        });

                        // 激活当前标签页
                        this.classList.add('active');
                        const content = document.getElementById(`${tabName}Content`);
                        if (content) {
                            content.classList.add('active');
                            content.style.display = 'block';
                            console.log('成功切换到标签页:', tabName);
                        }
                    });
                });
                classmatesTabsInitialized = true;
            }
        }

        // 安全的从搜索结果中显示学生详情（使用缓存键）
        function showStudentFromSearchSafe(studentName, cacheKey) {
            // 从临时缓存中获取学生信息
            const studentInfo = window.tempStudentInfoCache && window.tempStudentInfoCache[cacheKey];

            if (studentInfo) {
                showStudentFromSearch(studentName, studentInfo);
            } else {
                // 如果缓存中没有信息，使用原始方法
                showStudentFromSearch(studentName, null);
            }
        }

        // 从搜索结果中显示学生详情
        function showStudentFromSearch(studentName, studentInfo = null) {
            // 不关闭搜索结果模态框，让用户可以在两个窗口之间切换
            // 只是将搜索结果模态框暂时隐藏，但保持数据
            document.getElementById('classmatesModal').style.display = 'none';

            // 统一使用搜索结果中的信息，确保信息显示的一致性
            if (studentInfo) {
                // 直接使用搜索结果中的信息
                showStudentDetailsWithInfo(studentName, studentInfo);
            } else {
                // 如果没有传入学生信息，则调用原始方法
                showStudentDetails(studentName);
            }

            // 在学生详情模态框中添加一个"返回搜索结果"按钮
            addBackToSearchButton();
        }

        // 添加返回搜索结果按钮
        function addBackToSearchButton() {
            const studentModal = document.getElementById('studentModal');
            const modalContent = studentModal.querySelector('.modal-content');

            // 检查是否已经存在返回按钮
            let backButton = studentModal.querySelector('.back-to-search-btn');
            if (!backButton) {
                backButton = document.createElement('button');
                backButton.className = 'back-to-search-btn';
                backButton.innerHTML = '← 返回搜索结果';
                backButton.style.cssText = `
                    position: absolute;
                    top: 15px;
                    left: 15px;
                    background: #4CAF50;
                    color: white;
                    border: none;
                    padding: 6px 12px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 12px;
                    z-index: 10;
                `;
                backButton.onclick = function() {
                    document.getElementById('studentModal').style.display = 'none';
                    document.getElementById('classmatesModal').style.display = 'block';
                };
                modalContent.appendChild(backButton);
            }
            backButton.style.display = 'block';
        }

        // 直接使用已有信息显示学生详情（用于搜索结果中的学生）
        function showStudentDetailsWithInfo(studentName, studentInfo) {
            const modal = document.getElementById('studentModal');
            const modalAvatar = document.getElementById('modalStudentAvatar');
            const modalName = document.getElementById('modalStudentName');
            const modalPinyin = document.getElementById('modalPinyin');
            const modalSchool = document.getElementById('modalSchool');
            const modalGrade = document.getElementById('modalGrade');
            const modalClass = document.getElementById('modalClass');
            const modalAddress = document.getElementById('modalAddress');
            const modalRemark = document.getElementById('modalRemark');
            const locationLink = modal.querySelector('.location-link');
            const phoneList = document.getElementById('phoneList');
            const modalSignInTime = document.getElementById('modalSignInTime');
            const modalSignOutTime = document.getElementById('modalSignOutTime');
            const deleteStudentBtn = document.getElementById('deleteStudentBtn');

            // 只在管理员登录时显示删除按钮
            if (localStorage.getItem('currentUser') === 'ling') {
                deleteStudentBtn.style.display = 'inline-block';
            } else {
                deleteStudentBtn.style.display = 'none';
            }

            // 设置模态框关闭事件
            const closeBtn = modal.querySelector('.close-modal');
            if (closeBtn) {
                closeBtn.onclick = () => {
                    modal.style.display = 'none';
                    document.querySelector('.search-button').classList.remove('active');
                };
            }

            // 点击模态框外部关闭
            modal.onclick = function(event) {
                if (event.target === modal) {
                    modal.style.display = 'none';
                    document.querySelector('.search-button').classList.remove('active');
                }
            };

            // 设置学生姓名
            modalName.textContent = studentName;

            // 设置头像
            const avatarSrc = `./FaceID/学生照片/${nameToPinyin[studentName] || ''}.jpg?t=${new Date().getTime()}`;
            modalAvatar.src = avatarSrc;
            modalAvatar.onerror = function() {
                this.src = './node_modules/默认头像.png';
                this.classList.add('default-avatar');
            };
            modalAvatar.onload = () => {
                modalAvatar.classList.remove('default-avatar');
            };

            // 设置拼音
            modalPinyin.textContent = nameToPinyin[studentName] || '未设置';

            // 设置学校信息
            modalSchool.value = studentInfo.school || '';
            modalGrade.value = studentInfo.grade || '';
            modalClass.value = studentInfo.class || '';
            modalAddress.value = studentInfo.address || '';
            modalRemark.value = studentInfo.notes || '';

            // 设置地图链接
            updateLocationLink();

            // 处理电话信息
            phoneList.innerHTML = '';
            if (studentInfo.phone && studentInfo.phone.trim()) {
                const phones = studentInfo.phone.split('&').filter(p => p.trim());
                phones.forEach(phone => {
                    addPhoneNumberToList(phone.trim());
                });
            } else {
                addPhoneNumberToList(''); // 添加一个空的电话输入框
            }

            // 设置签到状态（搜索结果中的学生通常没有签到信息）
            modalSignInTime.textContent = '未签到';
            modalSignOutTime.textContent = '未签退';

            // 保存当前学生信息到全局变量
            currentStudentGrade = studentInfo.grade ? studentInfo.grade.replace('年级', '') : '';
            currentStudentName = studentName;

            // 显示模态框
            modal.style.display = 'block';

            // 绑定保存事件
            setupSaveEvents();

            // 设置签到记录按钮事件
            setupAttendanceRecordButton(studentName);
        }

        // 修改原有的showStudentDetails函数，在显示时隐藏返回按钮（如果不是从搜索结果来的）
        const originalShowStudentDetails = showStudentDetails;
        showStudentDetails = function(studentName) {
            originalShowStudentDetails(studentName);

            // 如果不是从搜索结果来的，隐藏返回按钮
            const backButton = document.querySelector('.back-to-search-btn');
            if (backButton && document.getElementById('classmatesModal').style.display !== 'block') {
                backButton.style.display = 'none';
            }
        };

        // 删除学生函数
        async function deleteStudentFromGrade() {
            const grade = currentStudentGrade;
            const studentName = currentStudentName;
            
            // 确认删除
            if (!confirm(`确定从${grade}年级删除 [${studentName}] 吗？`)) {
                return;
            }
            
            // 参数检查和日志输出（调试用）
            if (!grade || !studentName) {
                console.error("参数缺失:", {grade, studentName});
                alert("错误：缺少年级或学生姓名");
                return;
            }
            
            try {
                // 打印将要发送的数据（调试用）
                console.log("发送数据:", {
                    grade: grade,
                    action: "remove",
                    studentName: studentName
                });
                
                const response = await fetch('/api/grade/students', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        grade: grade,         // 确保使用正确的参数名
                        action: "remove",     // 固定为remove操作
                        studentName: studentName  // 确保使用正确的参数名
                    })
                });
                
                const result = await response.json();
                if (result.success) {
			    	// 发送WebSocket消息，通知所有客户端刷新
                    sendMessage('cardClick', `${grade}年级减${studentName}`);
					// sendMessage('cardClick', `${grade}年级-${studentName}`);
                    alert(result.message);
                    // 刷新本地页面
                    location.reload();
                } else {
                    alert(`删除失败: ${result.message}`);
                }
            } catch (error) {
                console.error("API调用错误:", error);
                alert(`操作失败: ${error.message}`);
            }
        }

        // 添加保存按钮到模态框
        document.querySelector('.modal-content').insertAdjacentHTML('beforeend', `
            <div style="text-align: center; margin: 1px auto; width: calc(100% - 30px); padding: 0; box-sizing: border-box; position: relative; left: 15px;">
                <button onclick="saveStudentInfo()" 
                        style="background: #1976d2; color: white; border: none; padding: 8px 20px; 
                               border-radius: 4px; cursor: pointer; font-size: 14px; width: 120px; display: block; margin: 0 auto;">
                    保存修改
                </button>
            </div>
        `);

        // 关闭模态框
        document.querySelector('.close-modal').addEventListener('click', () => {
            document.getElementById('studentModal').style.display = 'none';
            // 隐藏返回搜索结果按钮
            const backButton = document.querySelector('.back-to-search-btn');
            if (backButton) {
                backButton.style.display = 'none';
            }
        });

        // 点击模态框外部关闭
        window.addEventListener('click', (e) => {
            const modal = document.getElementById('studentModal');
            if (e.target === modal) {
                modal.style.display = 'none';
                // 隐藏返回搜索结果按钮
                const backButton = document.querySelector('.back-to-search-btn');
                if (backButton) {
                    backButton.style.display = 'none';
                }
            }
        });
        // 添加消息提示函数
        function showMessage(message, type = 'info') {
            // 获取或创建消息容器
            let container = document.querySelector('.modal-content .message-container');
            if (!container) {
                container = document.createElement('div');
                container.className = 'message-container';
                // 将容器插入到模态框内容的顶部
                const modalContent = document.querySelector('.modal-content');
                if (modalContent) {
                    modalContent.insertBefore(container, modalContent.firstChild);
                } else {
                    return; // 如果没有找到模态框，不显示消息
                }
            }
            
            // 创建消息元素
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = message;
            
            // 添加到容器
            container.appendChild(messageDiv);
            
            // 2秒后开始淡出
            setTimeout(() => {
                messageDiv.classList.add('fade-out');
                // 动画结束后移除元素
                setTimeout(() => {
                    if (container.contains(messageDiv)) {
                        container.removeChild(messageDiv);
                    }
                    // 如果没有更多消息，移除容器
                    if (container.children.length === 0) {
                        container.remove();
                    }
                }, 300);
            }, 2000);
        }

        // 修改未签到统计显示函数
        function updateUnsignDisplay() {
            const unsignContainer = document.querySelector('.unsign-summary');
            if (!unsignContainer) return;

            // 保存当前选中的学生
            const selectedStudents = Array.from(document.querySelectorAll('.student-card.selected'))
                .map(card => card.getAttribute('data-name'));

            // 获取每个年级的未签到学生
            const gradeUnsignedStudents = {};
            let hasUnsignedStudents = false;
            
            // 获取当前显示的年级区块
            const existingGradeSections = {};
            unsignContainer.querySelectorAll('.grade-section').forEach(section => {
                const gradeTitle = section.querySelector('.grade-section-title');
                if (gradeTitle) {
                    const grade = parseInt(gradeTitle.textContent);
                    existingGradeSections[grade] = section;
                }
            });

            // 遍历每个年级
            for (let grade = 1; grade <= 9; grade++) {
                const students = studentsData[grade] || [];
                const unsignedStudents = students.filter(name => {
                    const info = studentDetails[name] || {};
                    return !info['签到'];
                });

                if (unsignedStudents.length > 0) {
                    hasUnsignedStudents = true;
                    gradeUnsignedStudents[grade] = unsignedStudents;

                    // 检查这个年级是否已经显示
                    if (existingGradeSections[grade]) {
                        const section = existingGradeSections[grade];
                        const existingStudents = Array.from(section.querySelectorAll('.student-card'))
                            .map(card => card.getAttribute('data-name'));

                        // 比较学生列表是否有变化
                        if (!haveSameElements(existingStudents, unsignedStudents)) {
                            // 只更新发生变化的卡片
                            const studentsContainer = section.querySelector('.unsign-students');
                            if (studentsContainer) {
                                // 保存现有卡片的引用
                                const existingCards = {};
                                studentsContainer.querySelectorAll('.student-card').forEach(card => {
                                    const name = card.getAttribute('data-name');
                                    existingCards[name] = card;
                                });

                                // 清空容器
                                studentsContainer.innerHTML = '';

                                // 重新添加卡片，优先使用现有卡片
                                unsignedStudents.forEach(name => {
                                    if (existingCards[name]) {
                                        // 如果卡片已存在，直接使用
                                        studentsContainer.appendChild(existingCards[name]);
                                    } else {
                                        // 如果是新卡片，创建新的
                                        const newCard = createStudentCard(name, selectedStudents.includes(name));
                                        bindCardEvents(newCard, name);
                                        studentsContainer.appendChild(newCard);
                                    }
                                });
                            }

                            // 更新计数
                            const countElement = section.querySelector('.grade-section-count');
                            if (countElement) {
                                countElement.textContent = `${unsignedStudents.length}人未签到`;
                            }
                        }
                        
                        // 从待处理列表中移除
                        delete existingGradeSections[grade];
                        } else {
                        // 如果年级不存在，创建新的年级区块
                        const gradeSection = createGradeSection(grade, unsignedStudents, selectedStudents, 'sign');
                        unsignContainer.appendChild(gradeSection);
                    }
                }
            }

            // 移除不再需要的年级区块
            Object.values(existingGradeSections).forEach(section => {
                section.remove();
            });

            if (!hasUnsignedStudents) {
                unsignContainer.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #666; font-size: 16px;">
                        <div style="margin-bottom: 10px;">👏</div>
                        太棒了！目前没有未签到的学生
                    </div>
                `;
            }

            // 更新选中数量显示
            updateSelectedCount();
        }

        // 创建学生卡片的辅助函数
        function createStudentCard(name, isSelected = false) {
            const info = studentDetails[name] || {};
            const checkInTime = info['签到'] ? formatTimeForCard(info['签到']) : '--:--';
            const checkOutTime = info['签退'] ? formatTimeForCard(info['签退']) : '--:--';
            
            const card = document.createElement('div');
            card.className = `student-card${info['签到'] ? ' signed' : ''}${leaveStudents.has(name) ? ' on-leave' : ''}${isSelected ? ' selected' : ''}`;
            card.setAttribute('data-name', name);
            
            card.innerHTML = `
                <div class="student-avatar">
                    <img src="./FaceID/学生照片/${nameToPinyin[name] || ''}.jpg?t=${new Date().getTime()}" 
                         alt="${name}"
                         onerror="this.src='./node_modules/默认头像.png'">
                </div>
                <div class="student-name">${name}</div>
                <div class="time-info">
                    ${checkInTime !== '--:--' ? `<span class="check-in-time">${checkInTime}</span>` : ''}
                    ${checkOutTime !== '--:--' ? `<span class="check-out-time">${checkOutTime}</span>` : ''}
                </div>
                <div class="school-info">
                    ${info['学校'] || ''} ${info['年级'] || ''} ${info['班级'] || ''}班
                </div>
                ${leaveStudents.has(name) ? '<div class="leave-badge">假</div>' : ''}
                <button class="info-button" aria-label="查看学生信息" style="opacity: ${isSelected ? '1' : '0'}; transform: translateY(${isSelected ? '0' : '-10px'});">
                    <svg viewBox="0 0 24 24" width="20" height="20">
                        <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
                    </svg>
                </button>
            `;
            
            return card;
        }

        // 创建年级区块的辅助函数
        function createGradeSection(grade, students, selectedStudents, type) {
            const section = document.createElement('div');
            section.className = 'grade-section';
            
            const studentsHtml = students.map(name => {
                const isSelected = selectedStudents.includes(name);
                const card = createStudentCard(name, isSelected);
                return card.outerHTML;
            }).join('');

            section.innerHTML = `
                <div class="grade-section-header">
                    <div class="grade-section-left">
                        <div class="grade-section-title">${grade}年级</div>
                        <div class="grade-section-count">${students.length}人未${type === 'sign' ? '签到' : '签退'}</div>
                    </div>
                    <button class="batch-sign-button" data-grade="${grade}">批量${type === 'sign' ? '签到' : '签退'}</button>
                </div>
                <div class="unsign-students">
                    ${studentsHtml}
                </div>
            `;

            // 绑定事件
            const cards = section.querySelectorAll('.student-card');
            cards.forEach(card => {
                const name = card.getAttribute('data-name');
                bindCardEvents(card, name);
            });

            // 更新按钮文字
            updateBatchSignButton(grade, type);

            return section;
        }

        // 同样修改未签退统计显示函数
        function updateUnsignoutDisplay() {
            const unsignoutContainer = document.querySelector('#grade-unsignout .unsign-summary');
            if (!unsignoutContainer) return;

            // 保存当前选中的学生
            const selectedStudents = Array.from(unsignoutContainer.querySelectorAll('.student-card.selected'))
                .map(card => card.getAttribute('data-name'));

            // 保存当前已存在的卡片信息
            const existingCards = {};
            unsignoutContainer.querySelectorAll('.student-card').forEach(card => {
                const name = card.getAttribute('data-name');
                const avatar = card.querySelector('.student-avatar img');
                if (avatar && avatar.complete && avatar.naturalHeight !== 0) {
                    existingCards[name] = {
                        element: card.cloneNode(true),
                        avatar: avatar.cloneNode(true)
                    };
                }
            });

            unsignoutContainer.innerHTML = '';
            let hasUnsignoutStudents = false;

            // 遍历每个年级
            for (let grade = 1; grade <= 9; grade++) {
                const students = studentsData[grade] || [];
                const unsignoutStudents = students.filter(name => {
                    const info = studentDetails[name] || {};
                    // 只有已签到但未签退的学生才算未签退
                    return info['签到'] && !info['签退'];
                });

                if (unsignoutStudents.length > 0) {
                    hasUnsignoutStudents = true;
                    const gradeSection = document.createElement('div');
                    gradeSection.className = 'grade-section';
                    
                    const studentsHtml = unsignoutStudents.map(name => {
                        if (existingCards[name]) {
                            // 如果存在已加载的卡片，复用它
                            const card = existingCards[name].element;
                            // 更新必要的状态
                            const info = studentDetails[name] || {};
                            card.className = `student-card${info['签到'] ? ' signed' : ''}${leaveStudents.has(name) ? ' on-leave' : ''}`;
                            return card.outerHTML;
                        } else {
                            // 创建新卡片
                            const info = studentDetails[name] || {};
                            return `
                                <div class="student-card${info['签到'] ? ' signed' : ''}${leaveStudents.has(name) ? ' on-leave' : ''}" data-name="${name}">
                                    <div class="student-avatar">
                                        <img src="./FaceID/学生照片/${nameToPinyin[name] || ''}.jpg?t=${new Date().getTime()}" 
                                             alt="${name}"
                                             onerror="this.src='./node_modules/默认头像.png'">
                                    </div>
                                    <div class="student-name">${name}</div>
                                    <div class="school-info">
                                        ${studentDetails[name]?.['学校'] || ''} ${studentDetails[name]?.['年级'] || ''} ${studentDetails[name]?.['班级'] || ''}班
                                    </div>
                                    ${leaveStudents.has(name) ? '<div class="leave-badge">假</div>' : ''}
                                    <div class="sign-status"></div>
                                    <button class="info-button" aria-label="查看学生信息" style="opacity: 0; transform: translateY(-10px);">
                                        <svg viewBox="0 0 24 24" width="20" height="20">
                                            <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
                                        </svg>
                                    </button>
                                </div>
                            `;
                        }
                    }).join('');

                    gradeSection.innerHTML = `
                        <div class="grade-section-header">
                            <div class="grade-section-left">
                                <div class="grade-section-title">${grade}年级</div>
                                <div class="grade-section-count">${unsignoutStudents.length}人未签退</div>
                            </div>
                            <button class="batch-sign-button" data-grade="${grade}">批量签退</button>
                        </div>
                        <div class="unsign-students">
                            ${studentsHtml}
                        </div>
                    `;
                    
                    unsignoutContainer.appendChild(gradeSection);

                    // 为新添加的卡片绑定事件
                    const cards = gradeSection.querySelectorAll('.student-card');
                    cards.forEach(card => {
                        const name = card.getAttribute('data-name');
                        bindCardEvents(card, name);
                    });

                    // 更新批量签退按钮文字
                    updateBatchSignButton(grade, 'signout');
                }
            }

            if (!hasUnsignoutStudents) {
                unsignoutContainer.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #666; font-size: 16px;">
                        <div style="margin-bottom: 10px;">👏</div>
                        太棒了！目前没有未签退的学生
                    </div>
                `;
            }

            // 恢复选中状态
            selectedStudents.forEach(name => {
                const card = document.querySelector(`.unsign-students .student-card[data-name="${name}"]`);
                if (card) {
                    card.classList.add('selected');
                    const points = card.querySelector('.student-points');
                    const infoButton = card.querySelector('.info-button');
                    if (points) {
                        points.style.opacity = '1';
                        points.style.transform = 'translateY(0)';
                    }
                    if (infoButton) {
                        infoButton.style.opacity = '1';
                        infoButton.style.transform = 'translateY(0)';
                        }
                    }
                });
        }

        // 添加更新指定年级显示的函数
        function updateGradeDisplay(grade) {
            const container = document.getElementById(`grade-${grade}`);
            if (container && studentsData[grade]) {
                studentsData[grade].forEach(name => {
                    const card = container.querySelector(`.student-card[data-name="${name}"]`);
                    if (card) {
                        const studentInfo = studentDetails[name] || {};
                        const checkInTime = formatTime(studentInfo['签到'], false);
                        const checkOutTime = formatTime(studentInfo['签退'], false);
                        
                        // 更新签到状态
                        card.className = `student-card${studentInfo['签到'] ? ' signed' : ''}`;
                        
                        // 更新时间信息
                        const timeInfo = card.querySelector('.time-info');
                        if (timeInfo) {
                            timeInfo.innerHTML = `
                                ${checkInTime !== '--:--' ? `<span class="check-in-time">${checkInTime}</span>` : ''}
                                ${checkOutTime !== '--:--' ? `<span class="check-out-time">${checkOutTime}</span>` : ''}
                            `;
                        }
                    }
                });
            }
        }

        // 更新状态栏信息
        function updateStatusBar(grade) {
            const statusGrade = DOMCache.get('.status-grade');
            const statusTotal = DOMCache.get('.status-total');
            const statusSigned = DOMCache.get('.status-signed');
            const statusUnsigned = DOMCache.get('.status-unsigned');
            const statusLeave = DOMCache.get('.status-leave');
            const statusDate = DOMCache.get('.status-date');

            if (grade === 'all') {
                updateStatusBarAll();
                return;
            }

            // 获取当前年级的学生数据
            const students = studentsData[grade] || [];
            const totalCount = students.length;
            const signedCount = students.filter(name => studentDetails[name]?.['签到']).length;
            
            // 修改请假统计逻辑：只统计未签到的请假学生
            const leaveCount = students.filter(name => 
                leaveStudents.has(name) && !studentDetails[name]?.['签到']
            ).length;
            
            // 未签到人数需要减去请假人数
            const unsignedCount = totalCount - signedCount;

            // 更新统计信息
            statusGrade.textContent = `${grade}年级`;
            statusTotal.textContent = `总人数：${totalCount}人`;
            statusSigned.textContent = `已签到：${signedCount}人`;
            statusUnsigned.textContent = `未签到：${unsignedCount}人`;
            statusLeave.textContent = `请假：${leaveCount}人`;

            // 更新日期信息
            const now = new Date();
            const weekDays = ['日', '一', '二', '三', '四', '五', '六'];
            const dateStr = `${now.getFullYear()}年${String(now.getMonth() + 1).padStart(2, '0')}月${String(now.getDate()).padStart(2, '0')}日`;
            const weekStr = `星期${weekDays[now.getDay()]}`;
            
            try {
                // 修改农历库检查逻辑，使用更安全的检查方式
                if (typeof Lunar !== 'undefined' && Lunar) {
                    const lunar = Lunar.fromDate(now);
                    const solar = Solar.fromDate(now);
                    
                    // 获取节气
                    const jieQi = lunar.getJieQi();
                    
                    // 获取农历和阳历节日
                    const festivals = [];
                    
                    // 获取农历节日
                    const lunarMonth = lunar.getMonth();
                    const lunarDay = lunar.getDay();
                    
                    // 添加小年判断（腊月廿三或廿四，南方是廿三，北方是廿四）
                    if (lunarMonth === 12 && (lunarDay === 23 || lunarDay === 24)) {
                        festivals.push('小年');
                    }
                    
                    // 获取其他传统节日
                    const traditionalFestivals = lunar.getFestivals() || [];
                    festivals.push(...traditionalFestivals);
                    
                    // 获取阳历节日
                    const solarFestivals = solar.getFestivals() || [];
                    
                    // 构建农历日期字符串
                    const lunarStr = `${lunar.getYearInGanZhi()}年  ${lunar.getMonthInChinese()}月  ${lunar.getDayInChinese()}`;
                    const jieQiStr = jieQi ? ` ${jieQi}` : '';
                    
                    // 构建节日字符串
                    let festivalStr = '';
                    const allFestivals = [...festivals, ...solarFestivals].filter(Boolean);
                    if (allFestivals.length > 0) {
                        festivalStr = ` [${allFestivals.join('/')}]`;
                    }
                    
                    // 更新日期显示
                    const statusDateElement = document.querySelector('.status-date');
                    statusDateElement.innerHTML = `<span>${dateStr}</span><span>${weekStr}</span>`;
                    document.querySelector('.lunar-date').textContent = lunarStr + jieQiStr + festivalStr + ' ';
                } else {
                    // 农历库未加载，只显示公历日期
                    const statusDateElement = document.querySelector('.status-date');
                    statusDateElement.innerHTML = `<span>${dateStr}</span><span>${weekStr}</span>`;
                    const lunarDateElement = document.querySelector('.lunar-date');
                    if (lunarDateElement) {
                        lunarDateElement.textContent = ''; // 清空农历显示
                    }
                }
            } catch (error) {
                console.error('更新日期时出错:', error);
                const statusDateElement = document.querySelector('.status-date');
                statusDateElement.innerHTML = `<span>${dateStr}</span><span>${weekStr}</span>`;
                const lunarDateElement = document.querySelector('.lunar-date');
                if (lunarDateElement) {
                    lunarDateElement.textContent = ''; // 出错时清空农历显示
                }
            }
        }

        // 修改总体统计函数，支持未签到和未签退两种模式
        function updateStatusBarAll(forceMode = null) {
            const statusGrade = document.querySelector('.status-grade');
            const statusTotal = document.querySelector('.status-total');
            const statusSigned = document.querySelector('.status-signed');
            const statusUnsigned = document.querySelector('.status-unsigned');
            const statusLeave = document.querySelector('.status-leave');

            // 获取当前视图模式，优先使用传入的模式参数
            let currentGradeMode = forceMode;
            if (!currentGradeMode) {
                // 尝试从DOM获取，如果失败则使用全局变量
                const activeTab = document.querySelector('.grade-tab.active');
                currentGradeMode = activeTab ? activeTab.getAttribute('data-grade') : window.currentGrade;
            }
            const isUnsignoutMode = currentGradeMode === 'unsignout';

            // 计算所有年级的总计
            let totalCount = 0;
            let signedCount = 0;
            let leaveCount = 0;

            // 遍历所有年级
            for (let grade = 1; grade <= 9; grade++) {
                const students = studentsData[grade] || [];
                totalCount += students.length;

                if (isUnsignoutMode) {
                    // 未签退模式：统计已签到的学生数量
                    const signedInCount = students.filter(name => studentDetails[name]?.['签到']).length;
                    // 统计已签退的学生数量
                    signedCount += students.filter(name => {
                        const info = studentDetails[name] || {};
                        return info['签到'] && info['签退'];
                    }).length;
                    // 请假统计：已签到但请假的学生
                    leaveCount += students.filter(name =>
                        leaveStudents.has(name) && studentDetails[name]?.['签到'] && !studentDetails[name]?.['签退']
                    ).length;
                } else {
                    // 未签到模式：原有逻辑
                    signedCount += students.filter(name => studentDetails[name]?.['签到']).length;
                    // 请假统计：只统计未签到的请假学生
                    leaveCount += students.filter(name =>
                        leaveStudents.has(name) && !studentDetails[name]?.['签到']
                    ).length;
                }
            }

            if (isUnsignoutMode) {
                // 未签退模式的统计
                const signedInCount = getAllSignedInStudents().length; // 已签到总数
                const unsignedOutCount = signedInCount - signedCount; // 未签退数 = 已签到数 - 已签退数

                statusGrade.textContent = '全校';
                statusTotal.textContent = `已签到：${signedInCount}人`;
                statusSigned.textContent = `已签退：${signedCount}人`;
                statusUnsigned.textContent = `未签退：${unsignedOutCount}人`;
                statusLeave.textContent = `请假：${leaveCount}人`;
            } else {
                // 未签到模式的统计
                const unsignedCount = totalCount - signedCount;

                statusGrade.textContent = '全校';
                statusTotal.textContent = `总人数：${totalCount}人`;
                statusSigned.textContent = `已签到：${signedCount}人`;
                statusUnsigned.textContent = `未签到：${unsignedCount}人`;
                statusLeave.textContent = `请假：${leaveCount}人`;
            }
        }

        // 获取所有已签到学生的辅助函数
        function getAllSignedInStudents() {
            const signedInStudents = [];
            for (let grade = 1; grade <= 9; grade++) {
                const students = studentsData[grade] || [];
                students.forEach(name => {
                    const info = studentDetails[name] || {};
                    if (info['签到']) {
                        signedInStudents.push(name);
                    }
                });
            }
            return signedInStudents;
        }

        // DOM缓存管理器
        const DOMCache = {
            elements: new Map(),

            get(selector) {
                if (!this.elements.has(selector)) {
                    const element = document.querySelector(selector);
                    if (element) {
                        this.elements.set(selector, element);
                    }
                }
                return this.elements.get(selector) || null;
            },

            getAll(selector) {
                return document.querySelectorAll(selector);
            },

            clear() {
                this.elements.clear();
            },

            remove(selector) {
                this.elements.delete(selector);
            }
        };

        // EventManager已在前面定义

        // 全局状态管理器
        const AppState = {
            currentGrade: null,
            currentUser: null,
            isAdmin: false,

            init() {
                this.currentUser = localStorage.getItem('currentUser');
                this.isAdmin = this.currentUser === 'ling';
                this.currentGrade = this.isAdmin ? '2' : this.currentUser;
            },

            setCurrentGrade(grade) {
                this.currentGrade = grade;
            },

            getCurrentGrade() {
                // 直接查询DOM，不使用缓存，因为active状态会动态变化
                const activeTab = document.querySelector('.grade-tab.active');
                return activeTab ? activeTab.getAttribute('data-grade') : this.currentGrade;
            }
        };

        // 批量DOM操作优化器
        const DOMBatch = {
            operations: [],

            add(operation) {
                this.operations.push(operation);
            },

            execute() {
                if (this.operations.length === 0) return;

                // 使用requestAnimationFrame确保在下一个重绘前执行
                requestAnimationFrame(() => {
                    // 创建文档片段来批量操作
                    const fragment = document.createDocumentFragment();

                    this.operations.forEach(op => {
                        try {
                            op();
                        } catch (error) {
                            console.error('批量DOM操作失败:', error);
                        }
                    });

                    this.operations = [];
                });
            },

            clear() {
                this.operations = [];
            }
        };

        // 性能监控工具
        const PerformanceMonitor = {
            timers: new Map(),

            start(label) {
                this.timers.set(label, performance.now());
            },

            end(label) {
                const startTime = this.timers.get(label);
                if (startTime) {
                    const duration = performance.now() - startTime;
                    console.log(`[性能] ${label}: ${duration.toFixed(2)}ms`);
                    this.timers.delete(label);
                    return duration;
                }
                return 0;
            },

            measure(label, fn) {
                this.start(label);
                const result = fn();
                this.end(label);
                return result;
            }
        };

        // 获取当前活跃年级的辅助函数
        function getCurrentGrade() {
            return AppState.getCurrentGrade();
        }

        // 添加时间更新函数
        function updateCurrentTime() {
            const topTimeElement = DOMCache.get('#currentTime');
            const now = new Date();
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            const timeStr = `${hours}:${minutes}:${seconds}`;

            if (topTimeElement) topTimeElement.textContent = `北京时间：${timeStr}`;
        }

        // 添加初始化默认年级的函数，确保正确显示
        function initializeDefaultGrade() {
            // 设置默认年级
            const currentUser = localStorage.getItem('currentUser');
            currentGrade = currentUser === 'ling' ? '2' : currentUser;  // 管理员默认显示二年级，普通教师显示对应年级

            // 设置对应年级按钮的选中状态
            document.querySelectorAll('.grade-tab').forEach(tab => {
                if (tab.getAttribute('data-grade') === currentGrade) {
                    tab.classList.add('active');
                } else {
                    tab.classList.remove('active');
                }
            });
            
            // 显示正确的年级容器
            document.querySelectorAll('.students-container').forEach(container => {
                const gradeId = container.id;
                if (gradeId === `grade-${currentGrade}`) {
                    container.classList.add('active');
                } else {
                    container.classList.remove('active');
                }
            });
            
            // 立即更新状态栏
            updateCurrentGradeDisplay();
            
            // 确保教师卡片容器初始为空
            const teacherCardsContainer = document.querySelector('#grade-teacher .teacher-cards');
            if (teacherCardsContainer) {
                teacherCardsContainer.innerHTML = '';
            }
        }

        // 时间更新已在页面底部统一初始化

        // 在脚本加载时立即调用初始化默认年级函数
        // 这将确保页面一开始就显示正确的年级，而不是先显示教师考勤
        initializeDefaultGrade();

        // 在页面加载完成后初始化状态栏和加载数据
        document.addEventListener('DOMContentLoaded', () => {
            // 验证管理器初始化
            // console.log('[系统] 管理器初始化状态:', {
            //     DOMCache: typeof DOMCache !== 'undefined',
            //     EventManager: typeof EventManager !== 'undefined',
            //     AppState: typeof AppState !== 'undefined',
            //     DOMBatch: typeof DOMBatch !== 'undefined',
            //     PerformanceMonitor: typeof PerformanceMonitor !== 'undefined'
            // });

            // 初始化应用状态
            AppState.init();

            // 立即更新用户信息显示
            updateLoginInfo();

            // 添加农历库加载失败的事件监听器
            document.addEventListener('lunarLoadFailed', function() {
                console.warn('农历库加载失败事件已触发，使用公历日期继续显示');
                // 确保状态栏更新，使用公历日期
                updateStatusBar(AppState.currentGrade);
            });
            
            // 不再重复设置默认年级，只加载数据
            loadStudentData().then(() => {
                updateStatusBar(AppState.currentGrade); // 使用当前设置的年级更新状态栏
            }).catch(error => {
                console.error('加载学生数据失败:', error);
                // 即使数据加载失败，仍然显示基本界面
                updateStatusBar(AppState.currentGrade);
            });

            // 添加显示模式切换功能
            const displayModeToggle = document.getElementById('displayModeToggle');
            displayModeToggle.addEventListener('click', () => {
                const studentsContainers = document.querySelectorAll('.students-container');
                displayModeToggle.classList.toggle('active');
                
                studentsContainers.forEach(container => {
                    container.classList.toggle('show-school-info');
                });

                // 更新按钮文字
                displayModeToggle.textContent = displayModeToggle.classList.contains('active') ? 
                    '显示签到时间' : '显示学校信息';
            });

            // 绑定状态栏点击事件
            const statusBar = DOMCache.get('.status-bar');
            if (statusBar) {
                statusBar.addEventListener('click', function() {
                    const currentGrade = getCurrentGrade();
                    if (currentGrade === 'teacher') {
                        // 教师考勤视图
                        showTeacherTimeDetails();
                    } else if (currentGrade === 'unsign' || currentGrade === 'unsignout') {
                        // 未签到或未签退视图
                        showAllGradesTimeDetails();
                    } else {
                        // 年级视图
                        showTimeDetails(currentGrade);
                    }
                });
            }

            // 检查按钮可见性
            updateSummaryButtonVisibility();

            // 初始化教师照片样式
            initTeacherPhotoStyles();

            // 初始化学生头像样式
            initStudentAvatarStyles();

            // WebSocket连接已在页面底部统一初始化

            // 页面卸载时清理资源
            window.addEventListener('beforeunload', () => {
                DOMCache.clear();
                DOMBatch.clear();
                PerformanceMonitor.timers.clear();
            });
        });

        // 修改电子课本按钮
        document.getElementById('ebookButton').removeEventListener('click', () => {
            window.open('xue.html', '_blank');
        });

        // 添加退出登录函数
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('currentUser');
                localStorage.removeItem('loginInfo');
                window.location.href = 'login.html';
            }
        }

        // 绑定退出登录按钮事件
        document.getElementById('logoutButton').addEventListener('click', logout);

        // 修改 WebSocket 连接代码
        let ws = null;
        let isConnecting = false;
        let reconnectAttempts = 0;
        const MAX_RECONNECT_ATTEMPTS = 5;
        const RECONNECT_DELAY = 1000;  // 减少重连延迟
        const MESSAGE_QUEUE = [];
        const MESSAGE_TIMEOUT = 2000;  // 减少超时时间

        // 连接 WebSocket
        function connectWebSocket() {
            if (isConnecting) return;
            isConnecting = true;

            try {
                const serverIP = 'm.ys0635.cn';  // 使用实际的服务器IP
                // 根据协议选择端口
                const wsPort = window.location.protocol === 'https:' ? '8443' : '8130';
                const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${wsProtocol}//${serverIP}:${wsPort}`;
                // console.log('尝试连接 WebSocket:', wsUrl);
                
                ws = new WebSocket(wsUrl);

                ws.onopen = () => {
                    console.log('WebSocket 连接已建立，状态:', ws.readyState);
                    isConnecting = false;
                    reconnectAttempts = 0;
                    
                    // 发送测试消息
                    const testMessage = {
                        type: 'Test',
                        message: '测试连接'
                    };
                    sendMessageWithTimeout(testMessage);
                    
                    // 处理消息队列
                    processMessageQueue();
                };

                ws.onclose = () => {
                    console.log('WebSocket 连接已断开');
                    ws = null;
                    
                    if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
                        reconnectAttempts++;
                        console.log(`尝试重新连接 (${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})...`);
                        setTimeout(connectWebSocket, RECONNECT_DELAY);
                                    } else {
                        console.error('达到最大重连次数，停止重连');
                    }
                };

                ws.onerror = (error) => {
                    console.error('WebSocket 错误:', error);
                    isConnecting = false;
                };

                ws.onmessage = (event) => {
                    //console.log('收到消息:', event.data);
                    try {
                        const response = JSON.parse(event.data);
                        if (response.type === 'confirmation') {
                            // console.log('服务器确认:', response.message);
                            // 处理确认消息...
                        } else if (response.type === 'refresh') {
                            console.log('服务器要求刷新数据:', response.message);
                            // 如果需要刷新页面数据，可以在这里调用相应的函数
                        } else if (response.type === 'attendance_change') {
                            // 考勤文件变化通知处理
                            console.log('考勤文件变化:', response.fileType, response.fileName);
                            
                            // 根据文件类型加载不同的数据
                            if (response.fileType === 'signIn' || response.fileType === 'signOut') {
                                // 保存当前的考勤状态用于比较
                                const oldAttendanceState = {};
                                for (const name in studentDetails) {
                                    if (studentDetails[name]) {
                                        oldAttendanceState[name] = {
                                            签到: studentDetails[name]['签到'],
                                            签退: studentDetails[name]['签退']
                                        };
                                    }
                                }
                                
                                // 加载学生考勤数据
                                loadAttendanceInfo().then(({ hasChanges, signInContent, signOutContent }) => {
                                    // 加载教师考勤数据（只有首次加载时才需要工号信息）
                                    loadTeacherData(signInContent, signOutContent, !window.teacherDataLoaded).then((teacherResult) => {
                                    if (hasChanges) {
                                        // 更新界面显示
                                        updateAllCardsStatus();
                                        
                                        // 更新状态栏（根据当前显示的视图）
                                        if (currentGrade === 'teacher') {
                                            updateTeacherStatusBarNew();
                                        } else if (currentGrade === 'unsign' || currentGrade === 'unsignout') {
                                            updateStatusBarAll();
                                    } else {
                                            updateStatusBar(currentGrade);
                                        }

                                        // 查找发生变化的学生
                                        const changedStudents = [];
                                        const deletedStudents = []; // 新增：记录被删除考勤的学生
                                        for (const name in studentDetails) {
                                            if (studentDetails[name] && oldAttendanceState[name]) {
                                                const isSignInChanged = studentDetails[name]['签到'] !== oldAttendanceState[name]['签到'];
                                                const isSignOutChanged = studentDetails[name]['签退'] !== oldAttendanceState[name]['签退'];
                                                
                                                // 判断是新增考勤还是删除考勤
                                                if (response.fileType === 'signIn' && isSignInChanged) {
                                                    if (studentDetails[name]['签到'] && !oldAttendanceState[name]['签到']) {
                                                        changedStudents.push(name); // 新增签到
                                                    } else if (!studentDetails[name]['签到'] && oldAttendanceState[name]['签到']) {
                                                        deletedStudents.push(name); // 删除签到
                                                    }
                                                } else if (response.fileType === 'signOut' && isSignOutChanged) {
                                                    if (studentDetails[name]['签退'] && !oldAttendanceState[name]['签退']) {
                                                        changedStudents.push(name); // 新增签退
                                                    } else if (!studentDetails[name]['签退'] && oldAttendanceState[name]['签退']) {
                                                        deletedStudents.push(name); // 删除签退
                                                    }
                                                }
                                            }
                                        }
                                        
                                        // 显示通知
                                        const fileTypeText = response.fileType === 'signIn' ? '签到' : '签退';
                                        
                                        if (changedStudents.length > 0) {
                                            // 只显示第一个变化的学生
                                            const studentName = changedStudents[0];
                                            let studentGrade = '';
                                            
                                            // 尝试获取学生的年级
                                            if (studentDetails[studentName] && studentDetails[studentName]['年级']) {
                                                studentGrade = studentDetails[studentName]['年级'];
                                            } else {
                                                // 从studentsData中查找学生所在的年级
                                                for (let grade = 1; grade <= 9; grade++) {
                                                    if (studentsData[grade] && studentsData[grade].includes(studentName)) {
                                                        studentGrade = `${grade}年级`;
                                                        break;
                                                    }
                                                }
                                            }
                                            
                                            const toastMessage = `${studentGrade}-[${studentName}]${fileTypeText}`;
                                            const avatarPath = getAvatarPath(studentName, false, null, true);
                                            showToast(toastMessage, 'info', { avatar: avatarPath });
                                        } else if (deletedStudents.length > 0) { // 处理删除考勤的情况
                                            // 只显示第一个删除考勤的学生
                                            const studentName = deletedStudents[0];
                                            let studentGrade = '';
                                            
                                            // 尝试获取学生的年级
                                            if (studentDetails[studentName] && studentDetails[studentName]['年级']) {
                                                studentGrade = studentDetails[studentName]['年级'];
                                            } else {
                                                // 从studentsData中查找学生所在的年级
                                                for (let grade = 1; grade <= 9; grade++) {
                                                    if (studentsData[grade] && studentsData[grade].includes(studentName)) {
                                                        studentGrade = `${grade}年级`;
                                                        break;
                                                    }
                                                }
                                            }
                                            
                                            const toastMessage = `${studentGrade}-[${studentName}]${fileTypeText}已删除`;
                                            const avatarPath = getAvatarPath(studentName, false, null, true);
                                            showToast(toastMessage, 'info', { avatar: avatarPath });
                                        } else {
                                            // 如果没有检测到具体的学生变化，显示通用消息
                                        showToast(`${fileTypeText}数据已更新`, 'info');
                                        }

                                        // 处理教师考勤变化
                                        if (teacherResult && teacherResult.hasChanges) {
                                            if (teacherResult.changedTeachers.length > 0) {
                                                // 显示第一个变化的教师
                                                const teacher = teacherResult.changedTeachers[0];
                                                const toastMessage = `[${teacher.name}]${teacher.type}`;
                                                const teacherData = teacherDetails[teacher.name];
                                                const workId = teacherData ? teacherData.workId : '';
                                                const avatarPath = getAvatarPath(teacher.name, true, workId, true);
                                                showToast(toastMessage, 'info', { avatar: avatarPath });
                                            } else if (teacherResult.deletedTeachers.length > 0) {
                                                // 显示第一个删除考勤的教师
                                                const teacher = teacherResult.deletedTeachers[0];
                                                const toastMessage = `[${teacher.name}]${teacher.type}已删除`;
                                                const teacherData = teacherDetails[teacher.name];
                                                const workId = teacherData ? teacherData.workId : '';
                                                const avatarPath = getAvatarPath(teacher.name, true, workId, true);
                                                showToast(toastMessage, 'info', { avatar: avatarPath });
                                            }
                                        }
                                        } else {
                                            // 即使学生考勤没变化，也要更新教师状态栏
                                            if (currentGrade === 'teacher') {
                                                updateTeacherStatusBarNew();
                                            }

                                            // 检查教师考勤是否有变化
                                            if (teacherResult && teacherResult.hasChanges) {
                                                if (teacherResult.changedTeachers.length > 0) {
                                                    const teacher = teacherResult.changedTeachers[0];
                                                    const toastMessage = `[${teacher.name}]${teacher.type}`;
                                                    const teacherData = teacherDetails[teacher.name];
                                                    const workId = teacherData ? teacherData.workId : '';
                                                    const avatarPath = getAvatarPath(teacher.name, true, workId, true);
                                                    showToast(toastMessage, 'info', { avatar: avatarPath });
                                                } else if (teacherResult.deletedTeachers.length > 0) {
                                                    const teacher = teacherResult.deletedTeachers[0];
                                                    const toastMessage = `[${teacher.name}]${teacher.type}已删除`;
                                                    const teacherData = teacherDetails[teacher.name];
                                                    const workId = teacherData ? teacherData.workId : '';
                                                    const avatarPath = getAvatarPath(teacher.name, true, workId, true);
                                                    showToast(toastMessage, 'info', { avatar: avatarPath });
                                                }
                                            }
                                    }
                                }).catch(error => {
                                        console.error('更新教师考勤信息失败:', error);
                                });
                            }).catch(error => {
                                    console.error('更新学生考勤信息失败:', error);
                            });
                            } else if (response.fileType === 'leave') {
                                // 加载请假信息
                                loadLeaveInfo().then(() => {
                                    // 更新界面显示
                                    updateAllCardsStatus();
                                    showToast('请假信息已更新', 'info');
                                }).catch(error => {
                                    console.error('更新请假信息失败:', error);
                                });
                            }
                        }
                    } catch (error) {
                        console.error('消息解析错误:', error);
                    }
                };
            } catch (error) {
                console.error('WebSocket 连接错误:', error);
                isConnecting = false;
            }
        }

        // 发送消息（带超时处理）
        function sendMessageWithTimeout(message) {
            return new Promise((resolve, reject) => {
                if (!ws || ws.readyState !== WebSocket.OPEN) {
                    console.log('WebSocket未连接，加入消息队列');
                    MESSAGE_QUEUE.push({ message, resolve, reject });
                    if (!ws) {
                        connectWebSocket();
                    }
                    return;
                }

                const timeout = setTimeout(() => {
                    reject(new Error('消息发送超时'));
                }, MESSAGE_TIMEOUT);

                try {
                    const messageStr = JSON.stringify(message);
                    ws.send(messageStr);
                    // console.log('消息已发送:', messageStr);
                    clearTimeout(timeout);
                    resolve();
            } catch (error) {
                    clearTimeout(timeout);
                    reject(error);
                }
            });
        }

        // 处理消息队列
        function processMessageQueue() {
            if (MESSAGE_QUEUE.length === 0 || !ws || ws.readyState !== WebSocket.OPEN) {
                return;
            }

            const { message, resolve, reject } = MESSAGE_QUEUE.shift();
            sendMessageWithTimeout(message)
                .then(resolve)
                .catch(reject)
                .finally(() => {
                    // 继续处理队列中的下一条消息
                    setTimeout(processMessageQueue, 100);
                });
        }

        // 初始化 WebSocket 连接
        connectWebSocket();

        // 在点击事件处理中使用新的发送函数
        function handleCardClick(name) {
            sendMessage('cardClick', `学生: ${name}`);
        }

        // 立即更新一次时间，然后启动定时器
        updateCurrentTime();
        setInterval(updateCurrentTime, 1000);
        
        // 检查学生列表是否需要更新
        function checkStudentListUpdate(type) {
            const isSignIn = type === 'sign';
            
            // 获取当前学生列表
            const currentStudents = isSignIn ? getAllUnsignedStudents() : getAllUnsignoutStudents();
            
            // 获取已显示的学生列表
            const selector = isSignIn ? '.unsign-summary .student-card' : '#grade-unsignout .unsign-summary .student-card';
            const oldStudents = Array.from(document.querySelectorAll(selector))
                .map(card => card.getAttribute('data-name'));
            
            // 比较两个列表是否真的有变化（忽略顺序）
            const needUpdate = !haveSameElements(currentStudents, oldStudents);
            
            if (needUpdate) {
                if (isSignIn) {
                    updateUnsignDisplay();
                } else {
                    updateUnsignoutDisplay();
                }
                // 使用适合当前视图的状态栏更新
                if (currentGrade === 'unsign' || currentGrade === 'unsignout') {
                    updateStatusBarAll();
                } else if (currentGrade === 'teacher') {
                    updateTeacherStatusBarNew();
                } else {
                    updateStatusBar(currentGrade);
                }
            } else {
                // 即使学生列表没有变化，也需要检查请假状态是否变化
                const container = isSignIn ? document.querySelector('.unsign-summary') : document.querySelector('#grade-unsignout .unsign-summary');
                if (container) {
                    let hasLeaveStatusChange = false;
                    
                    // 检查每个显示的学生卡片的请假状态是否变化
                    container.querySelectorAll('.student-card').forEach(card => {
                        const name = card.getAttribute('data-name');
                        const isOnLeaveInUI = card.classList.contains('on-leave');
                        const isOnLeaveInData = leaveStudents.has(name);
                        
                        if (isOnLeaveInUI !== isOnLeaveInData) {
                            hasLeaveStatusChange = true;
                        }
                    });
                    
                    // 如果有请假状态变化，更新视图
                    if (hasLeaveStatusChange) {
                        if (isSignIn) {
                            updateUnsignDisplay();
                        } else {
                            updateUnsignoutDisplay();
                        }
                    }
                }
            }
        }

        // 处理批量签到/签退
        async function handleBatchSign(grade, type) {
            const container = type === 'sign' ? document.querySelector('.unsign-summary') : document.querySelector('#grade-unsignout .unsign-summary');
            const selectedCards = container.querySelectorAll('.student-card.selected');
            
            if (selectedCards.length === 0) {
                showToast('请先选择要操作的学生');
                return;
            }
            
            const names = Array.from(selectedCards).map(card => card.getAttribute('data-name'));
            const action = type === 'sign' ? '签到' : '签退';
            
            // 发送测试消息
            if (type === 'sign') {
                // 批量签到
                sendMessage('cardClick', `强制${action}${names.join('-')}`);
            } else {
                // 批量签退 - 需要检查是否有未签到的学生
                const unsignedNames = names.filter(name => !studentDetails[name]?.['签到']);
                if (unsignedNames.length > 0) {
                    // 有未签到的学生，发送特殊消息
                    sendMessage('cardClick', `未签到强制签退${unsignedNames.join('-')}`);
                    
                    // 如果还有正常签退的学生，发送普通消息
                    const signedNames = names.filter(name => studentDetails[name]?.['签到']);
                    if (signedNames.length > 0) {
                        sendMessage('cardClick', `强制${action}${signedNames.join('-')}`);
                    }
                } else {
                    // 所有学生都已签到，发送普通签退消息
                    sendMessage('cardClick', `强制${action}${names.join('-')}`);
                }
            }
            
            // 处理选中的学生
            for (const name of names) {
                if (type === 'sign') {
                    studentDetails[name]['签到'] = true;
                } else {
                    studentDetails[name]['签退'] = true;
                }
            }
            
            // 取消所有学生的选中状态
            selectedCards.forEach(card => {
                card.classList.remove('selected');
                const points = card.querySelector('.student-points');
                const infoButton = card.querySelector('.info-button');
                if (points) {
                    points.style.opacity = '0';
                    points.style.transform = 'translateY(-10px)';
                }
                if (infoButton) {
                    infoButton.style.opacity = '0';
                    infoButton.style.transform = 'translateY(-10px)';
                }
            });
            
            // 更新选中数量显示
            updateSelectedCount();
            
            // 显示操作成功提示
            showToast(`已为${names.length}名学生完成${action}操作`);
            
            await updateDisplay();
        }

        // 添加提示框显示函数（支持头像）
        function showToast(message, type = 'info', options = {}) {
            // 检查是否已存在toast元素
            let toast = document.getElementById('toast-message');
            if (!toast) {
                // 创建新的toast元素
                toast = document.createElement('div');
                toast.id = 'toast-message';
                document.body.appendChild(toast);
            }

            // 设置基础样式
            Object.assign(toast.style, {
                position: 'fixed',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                background: 'rgba(0, 0, 0, 0.8)',
                color: 'white',
                padding: options.avatar ? '20px' : '15px 25px',
                borderRadius: '25px',
                fontSize: '16px',
                zIndex: '10000',
                transition: 'opacity 0.3s ease',
                opacity: '0',
                pointerEvents: 'none',
                display: 'flex',
                flexDirection: options.avatar ? 'column' : 'row',
                alignItems: 'center',
                justifyContent: 'center',
                gap: options.avatar ? '15px' : '10px',
                maxWidth: '350px',
                textAlign: 'center'
            });

            // 构建内容
            let content = '';
            if (options.avatar) {
                content += `<img src="${options.avatar}"
                           style="width: 80px; height: 80px; border-radius: 50%; object-fit: cover; border: 3px solid rgba(255,255,255,0.4); box-shadow: 0 4px 12px rgba(0,0,0,0.3);"
                           onerror="this.src='./node_modules/默认头像.png'">`;
            }
            content += `<span style="font-weight: 500; line-height: 1.3;">${message}</span>`;

            // 设置消息并显示
            toast.innerHTML = content;
            toast.style.opacity = '1';

            // 2秒后隐藏
            setTimeout(() => {
                toast.style.opacity = '0';
                // 等待过渡效果完成后移除元素
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 2000);
        }

        // 获取已加载头像的辅助函数
        // 优化：直接使用页面上已加载的头像，避免重复网络请求
        function getLoadedAvatar(name, isTeacher = false) {
            try {
                // 查找对应的卡片元素（学生和教师都使用相同的结构）
                const cardSelector = `.student-card[data-name="${name}"] .student-avatar img`;
                const avatarImg = document.querySelector(cardSelector);

                // 检查图片是否存在且已成功加载
                if (avatarImg && avatarImg.complete && avatarImg.naturalWidth > 0) {
                    // 确保不是默认头像或加载失败的图片
                    if (!avatarImg.src.includes('默认头像.png') && !avatarImg.src.includes('default')) {
                        return avatarImg.src;
                    }
                }

                return null;
            } catch (error) {
                console.warn('获取已加载头像失败:', error);
                return null;
            }
        }

        // 获取头像路径的辅助函数（已优化）
        // 优化策略：
        // 1. 学生头像：优先使用页面上已加载的头像，避免重复网络请求
        // 2. 教师头像：如果教师考勤页面已加载则使用已加载头像，否则生成路径
        // 3. 性能提升：减少网络请求，提高显示速度，确保头像一致性
        function getAvatarPath(name, isTeacher = false, workId = null, useLoaded = true) {
            // 优先尝试使用已加载的头像
            if (useLoaded) {
                const loadedAvatar = getLoadedAvatar(name, isTeacher);
                if (loadedAvatar) {
                    return loadedAvatar;
                }
            }

            // 如果没有已加载的头像，使用传统路径生成方式
            if (isTeacher) {
                // 教师头像路径，使用工号
                if (workId) {
                    return `./FaceID/老师照片/${workId}.jpg`;
                } else {
                    // 如果没有传入工号，尝试从teacherDetails中获取
                    const teacher = teacherDetails[name];
                    const teacherWorkId = teacher ? teacher.workId : '';
                    return `./FaceID/老师照片/${teacherWorkId || 'default'}.jpg`;
                }
            } else {
                // 学生头像路径
                const pinyin = nameToPinyin[name] || '';
                return `./FaceID/学生照片/${pinyin}.jpg`;
            }
        }

        // 更新按钮文字
        function updateBatchSignButton(grade, type) {
            const container = type === 'sign' ? document.querySelector('.unsign-summary') : document.querySelector('#grade-unsignout .unsign-summary');
            const gradeSection = container.querySelector(`.grade-section:nth-child(${grade})`);
            if (!gradeSection) return;

            const button = gradeSection.querySelector('.batch-sign-button');
            const selectedCount = gradeSection.querySelectorAll('.student-card.selected').length;
            
            if (selectedCount > 0) {
                button.textContent = `${type === 'sign' ? '签到' : '签退'} (${selectedCount}人)`;
            } else {
                button.textContent = `批量${type === 'sign' ? '签到' : '签退'}`;
            }
        }

        // 在学生卡片选中状态改变时更新按钮文字
        EventManager.delegate('body', '.student-card', 'click', function(e) {
            const card = this;
            const gradeSection = card.closest('.grade-section');
            if (!gradeSection) return;

            const grade = gradeSection.querySelector('.grade-section-title').textContent.replace('年级', '');
            const unsignoutContainer = DOMCache.get('#grade-unsignout');
            const type = unsignoutContainer && unsignoutContainer.style.display === 'block' ? 'signout' : 'sign';

            setTimeout(() => {
                updateBatchSignButton(grade, type);
            }, 0);
        });

        // 更新显示函数
        function updateDisplay() {
            // 遍历所有年级
            for (let grade = 1; grade <= 9; grade++) {
                const container = document.getElementById(`grade-${grade}`);
                if (container && studentsData[grade]) {
                    // 保存当前选中的学生
                    const selectedStudents = Array.from(container.querySelectorAll('.student-card.selected'))
                        .map(card => card.getAttribute('data-name'));
                    
                    // 更新每个学生的状态
                    studentsData[grade].forEach(name => {
                        const card = container.querySelector(`.student-card[data-name="${name}"]`);
                        if (card) {
                            const studentInfo = studentDetails[name] || {};
                            const checkInTime = formatTime(studentInfo['签到'], false);
                            const checkOutTime = formatTime(studentInfo['签退'], false);
                            
                            // 更新签到状态，同时保持选中状态
                            const isSelected = selectedStudents.includes(name);
                            card.className = `student-card${studentInfo['签到'] ? ' signed' : ''}${leaveStudents.has(name) ? ' on-leave' : ''}${isSelected ? ' selected' : ''}`;
                            
                            // 更新时间信息
                            const timeInfo = card.querySelector('.time-info');
                            if (timeInfo) {
                                timeInfo.innerHTML = `
                                    ${checkInTime !== '--:--' ? `<span class="check-in-time">${checkInTime}</span>` : ''}
                                    ${checkOutTime !== '--:--' ? `<span class="check-out-time">${checkOutTime}</span>` : ''}
                                `;
                            }
                            
                            // 如果是选中状态，确保点数和信息按钮的显示状态
                            if (isSelected) {
                                const points = card.querySelector('.student-points');
                                const infoButton = card.querySelector('.info-button');
                                if (points) {
                                    points.style.opacity = '1';
                                    points.style.transform = 'translateY(0)';
                                }
                                if (infoButton) {
                                    infoButton.style.opacity = '1';
                                    infoButton.style.transform = 'translateY(0)';
                                }
                            }
                        }
                    });
                }
            }
            
            // 更新选中数量显示
            updateSelectedCount();
        }

        // 通用的更新显示函数
        function updateStudentDisplay(type) {
            const isSignIn = type === 'sign';
            const container = isSignIn ? 
                document.querySelector('.unsign-summary') : 
                document.querySelector('#grade-unsignout .unsign-summary');
            if (!container) return;

            // 保存当前选中的学生
            const selectedStudents = Array.from(container.querySelectorAll('.student-card.selected'))
                .map(card => card.getAttribute('data-name'));

            // 保存当前已存在的卡片信息
            const existingCards = {};
            container.querySelectorAll('.student-card').forEach(card => {
                const name = card.getAttribute('data-name');
                const avatar = card.querySelector('.student-avatar img');
                if (avatar && avatar.complete && avatar.naturalHeight !== 0) {
                    existingCards[name] = {
                        element: card.cloneNode(true),
                        avatar: avatar.cloneNode(true)
                    };
                }
            });
            
            // 查找所有年级中已加载的头像，避免重复请求
            const loadedAvatars = {};
            for (let grade = 1; grade <= 9; grade++) {
                const gradeContainer = document.getElementById(`grade-${grade}`);
                if (gradeContainer) {
                    gradeContainer.querySelectorAll('.student-card').forEach(card => {
                        const name = card.getAttribute('data-name');
                        if (!name) return;
                        
                        const avatar = card.querySelector('.student-avatar img');
                        if (avatar && avatar.complete && avatar.naturalHeight !== 0 && avatar.src && !avatar.src.includes('默认头像.png')) {
                            loadedAvatars[name] = avatar.cloneNode(true);
                        }
                    });
                }
            }

            container.innerHTML = '';
            let hasStudents = false;

            // 获取当前登录用户
            const currentUser = localStorage.getItem('currentUser');
            const isAdmin = currentUser === 'ling';
            const userGrade = !isAdmin ? parseInt(currentUser) : null;

            // 遍历每个年级
            for (let grade = 1; grade <= 9; grade++) {
                // 如果是普通老师且不是其所在年级，跳过
                if (!isAdmin && grade !== userGrade) continue;

                const students = studentsData[grade] || [];
                const filteredStudents = students.filter(name => {
                    const info = studentDetails[name] || {};
                    return isSignIn ? !info['签到'] : (info['签到'] && !info['签退']);
                });

                if (filteredStudents.length > 0) {
                    hasStudents = true;
                    const gradeSection = document.createElement('div');
                    gradeSection.className = 'grade-section';

                    // 复用已存在的卡片或创建新卡片
                    const studentsHtml = filteredStudents.map(name => {
                        if (existingCards[name]) {
                            return existingCards[name].element.outerHTML;
                        } else {
                            const isSelected = selectedStudents.includes(name);
                            const info = studentDetails[name] || {};
                            
                            // 检查是否有已加载的头像可以复用
                            let avatarHtml = '';
                            if (loadedAvatars[name]) {
                                const loadedAvatar = loadedAvatars[name];
                                avatarHtml = `<div class="student-avatar">${loadedAvatar.outerHTML}</div>`;
                            } else {
                                // 如果没有已加载的头像，再创建新的图像元素
                                avatarHtml = `
                                    <div class="student-avatar">
                                        <img src="./FaceID/学生照片/${nameToPinyin[name] || ''}.jpg?t=${new Date().getTime()}" 
                                             alt="${name}"
                                             onerror="this.src='./node_modules/默认头像.png'">
                                    </div>
                                `;
                            }
                            
                            return `
                                <div class="student-card${info['签到'] ? ' signed' : ''}${leaveStudents.has(name) ? ' on-leave' : ''}${isSelected ? ' selected' : ''}" data-name="${name}">
                                    ${avatarHtml}
                                    <div class="student-name">${name}</div>
                                    <div class="school-info">
                                        ${info['学校'] || ''} ${info['年级'] || ''} ${info['班级'] || ''}班
                                    </div>
                                    ${leaveStudents.has(name) ? '<div class="leave-badge">假</div>' : ''}
                                    <div class="sign-status"></div>
                                    <button class="info-button" aria-label="查看学生信息" style="opacity: ${isSelected ? '1' : '0'}; transform: translateY(${isSelected ? '0' : '-10px'});">
                                        <svg viewBox="0 0 24 24" width="20" height="20">
                                            <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
                                        </svg>
                                    </button>
                        </div>
                    `;
                }
                    }).join('');

                    gradeSection.innerHTML = `
                        <div class="grade-section-header">
                            <div class="grade-section-left">
                                <div class="grade-section-title">${grade}年级</div>
                                <div class="grade-section-count">${filteredStudents.length}人未${isSignIn ? '签到' : '签退'}</div>
                            </div>
                            <button class="batch-sign-button" data-grade="${grade}" ${!isSignIn ? 'data-signout="true"' : ''}>批量${isSignIn ? '签到' : '签退'}</button>
                        </div>
                        <div class="unsign-students">
                            ${studentsHtml}
                        </div>
                    `;
                    
                    container.appendChild(gradeSection);

                    // 为新添加的卡片绑定事件
                    const cards = gradeSection.querySelectorAll('.student-card');
                    cards.forEach(card => {
                        const name = card.getAttribute('data-name');
                        bindCardEvents(card, name);
                    });

                    // 绑定批量签到/签退按钮事件
                    const batchSignButton = gradeSection.querySelector('.batch-sign-button');
                    batchSignButton.addEventListener('click', () => handleBatchSign(grade, isSignIn ? 'sign' : 'signout'));
                }
            }

            if (!hasStudents) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #666; font-size: 16px;">
                        <div style="margin-bottom: 10px;">👏</div>
                        太棒了！目前没有未${isSignIn ? '签到' : '签退'}的学生
                    </div>
                `;
            }

            // 恢复选中状态
            selectedStudents.forEach(name => {
                const card = document.querySelector(`.unsign-students .student-card[data-name="${name}"]`);
                if (card) {
                    card.classList.add('selected');
                    const points = card.querySelector('.student-points');
                    const infoButton = card.querySelector('.info-button');
                    if (points) {
                        points.style.opacity = '1';
                        points.style.transform = 'translateY(0)';
                    }
                    if (infoButton) {
                        infoButton.style.opacity = '1';
                        infoButton.style.transform = 'translateY(0)';
                    }
                }
            });
            
            // 更新选中数量显示
            updateSelectedCount();
        }

        // updateUnsignDisplay 和 updateUnsignoutDisplay 函数已在前面定义

        // 计算在校时长的函数
        function calculateDuration(signInTime, signOutTime) {
            if (signInTime === '--:--:--' || signOutTime === '--:--:--') {
                return { text: '--', minutes: 0 };
            }

            try {
                const [inHours, inMinutes, inSeconds] = signInTime.split(':').map(Number);
                const [outHours, outMinutes, outSeconds] = signOutTime.split(':').map(Number);
                
                if (isNaN(inHours) || isNaN(inMinutes) || isNaN(inSeconds) ||
                    isNaN(outHours) || isNaN(outMinutes) || isNaN(outSeconds)) {
                    return { text: '--', minutes: 0 };
                }
                
                const inMinutesTotal = inHours * 60 + inMinutes;
                const outMinutesTotal = outHours * 60 + outMinutes;
                
                const durationMinutes = outMinutesTotal - inMinutesTotal;
                
                if (durationMinutes <= 0) {
                    return { text: '--', minutes: 0 };
                }
                
                const hours = Math.floor(durationMinutes / 60);
                const minutes = durationMinutes % 60;
                
                return {
                    text: `${hours}小时${String(minutes).padStart(2, '0')}分`,
                    minutes: durationMinutes
                };
            } catch (error) {
                console.error('计算在校时长出错:', error);
                return { text: '--', minutes: 0 };
            }
        }

        // 添加左下角统计点击事件
        const statusLeft = DOMCache.get('.status-left');
        if (statusLeft) {
            statusLeft.addEventListener('click', function() {
                const currentGrade = getCurrentGrade();
                if (currentGrade === 'unsign' || currentGrade === 'unsignout') return;

                showTimeDetails(currentGrade);
            });
        }

        // 显示时间详情的函数
        function showTimeDetails(grade) {
            const modal = document.getElementById('timeDetailsModal');
            const title = document.getElementById('timeDetailsTitle');
            const list = document.getElementById('timeDetailsList');
            const modalContent = modal.querySelector('.modal-content');
            
            // 重置排序状态为默认（按签到时间排序）
            window.sortState = {
                field: 'signIn',
                direction: 'asc'
            };
            
            // 恢复原始样式
            if (modalContent) {
                modalContent.classList.add('time-details-modal');
                modalContent.classList.remove('teacher-time-details-modal');
            }
            
            // 清空列表
            list.innerHTML = '';
            
            // 设置标题
            title.textContent = `${grade}年级学生统计详情`;
            
            // 添加表头
            const header = document.createElement('div');
            header.className = 'time-details-header';
            header.innerHTML = `
                <span>
                    <svg onclick="copyNames()" viewBox="0 0 24 24" width="16" height="16" style="cursor: pointer; vertical-align: middle; margin-right: 4px;">
                        <path fill="currentColor" d="M16 1H4C2.9 1 2 1.9 2 3v14h2V3h12V1zm3 4H8C6.9 5 6 5.9 6 7v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                    </svg>
                    <span onclick="sortTimeDetails('name')" style="cursor: pointer;">姓名</span>
                </span>
                <span onclick="sortTimeDetails('points')" style="cursor: pointer;">积分</span>
                <span onclick="sortTimeDetails('signIn')" style="cursor: pointer;">签到时间</span>
                <span onclick="sortTimeDetails('signOut')" style="cursor: pointer;">签退时间</span>
                <span onclick="sortTimeDetails('duration')" style="cursor: pointer;">在校时长</span>
            `;
            list.appendChild(header);
            
            // 获取当前年级的所有学生
            const students = studentsData[grade] || [];
            
            // 创建学生签到时间列表
            const timeDetails = students.map(name => {
                const info = studentDetails[name] || {};
                const signInTime = info['签到'] ? formatTime(info['签到'], true) : '';
                const signOutTime = info['签退'] ? formatTime(info['签退'], true) : '';
                const duration = calculateDuration(signInTime, signOutTime);
                const school = info['学校'] || '';
                const classNum = info['班级'] || '';
                const points = studentPoints[name] || '0'; // 获取学生积分
                return { 
                    name, 
                    signInTime, 
                    signOutTime, 
                    duration: duration.text,
                    school,
                    classNum,
                    points  // 添加积分字段
                };
            });
            
            // 按签到时间排序（未签到的排在最后）
            timeDetails.sort((a, b) => {
                if (!a.signInTime && b.signInTime) return 1;
                if (a.signInTime && !b.signInTime) return -1;
                return a.signInTime.localeCompare(b.signInTime);
            });
            
            // 生成HTML
            timeDetails.forEach(({ name, signInTime, signOutTime, duration, school, classNum, points }) => {
                const div = document.createElement('div');
                div.className = 'time-details-item';
                
                // 根据学校显示不同样式的班级信息
                const showClass = school === '滨河实验' || school === '东苑中学';
                let displayName = name;
                if (showClass) {
                    const classStyle = school === '东苑中学' ? 'color: #ff4444;' : 'color: #1976d2;';
                    displayName = `${name}<span style="${classStyle}">${classNum}</span>`;
                }
                
                div.innerHTML = `
                    <span class="time-details-name">
                        <span class="student-name-link" onclick="showStudentDetails('${name}')" 
                              style="cursor: pointer; color: #1976d2; text-decoration: none;">
                            ${displayName}
                        </span>
                    </span>
                    <span class="time-details-points">
                        <span class="points-box">${points}</span>
                    </span>
                    <span class="time-details-signin">
                        ${signInTime !== '--:--:--' ? 
                            `<span class="time-box" style="color: #4caf50; background-color: #e8f5e9">${signInTime}</span>` : 
                            ``}
                    </span>
                    <span class="time-details-signout">
                        ${signOutTime !== '--:--:--' ? 
                            `<span class="time-box" style="color: #ff4444; background-color: #ffebee">${signOutTime}</span>` : 
                            ``}
                    </span>
                    <span class="time-details-duration">
                        ${duration && duration !== '--' ? 
                            `<span class="time-box" style="color: #1976d2; background-color: #e3f2fd">${duration}</span>` : 
                            ''}
                    </span>
                `;
                list.appendChild(div);
            });
            
            // 显示模态框
            modal.style.display = 'block';
        }

        // 更新其他使用卡片时间显示的函数
        function updateCardTime(card, studentInfo) {
            const checkInTime = studentInfo['签到'] ? formatTimeForCard(studentInfo['签到']) : '--:--';
            const checkOutTime = studentInfo['签退'] ? formatTimeForCard(studentInfo['签退']) : '--:--';
            
            const timeInfo = card.querySelector('.time-info');
            if (timeInfo) {
                timeInfo.innerHTML = `
                    ${checkInTime !== '--:--' ? `<span class="check-in-time">${checkInTime}</span>` : ''}
                    ${checkOutTime !== '--:--' ? `<span class="check-out-time">${checkOutTime}</span>` : ''}
                `;
            }
        }

        // 添加全局排序状态
        window.sortState = {
            field: 'signIn',
            direction: 'asc'
        };

        // 修改排序函数
        window.sortTimeDetails = function(field) {
            // 清除所有排序状态
            const nameHeader = document.querySelector('.time-details-header span > span');
            const otherHeaders = document.querySelectorAll('.time-details-header > span:not(:first-child)');
            
            // 清除所有排序状态
            if (nameHeader) nameHeader.classList.remove('sort-asc', 'sort-desc');
            otherHeaders.forEach(header => {
                header.classList.remove('sort-asc', 'sort-desc');
            });

            if (window.sortState.field === field) {
                window.sortState.direction = window.sortState.direction === 'asc' ? 'desc' : 'asc';
                } else {
                window.sortState.field = field;
                window.sortState.direction = 'asc';
            }

            // 更新排序箭头
            if (field === 'name') {
                nameHeader.classList.add(`sort-${window.sortState.direction}`);
            } else {
                const columnMap = {
                    'points': 0,  // 新增积分列的索引
                    'signIn': 1,
                    'signOut': 2,
                    'duration': 3
                };
                const columnIndex = columnMap[field];
                if (columnIndex !== undefined && otherHeaders[columnIndex]) {
                    otherHeaders[columnIndex].classList.add(`sort-${window.sortState.direction}`);
                }
            }

            const list = document.getElementById('timeDetailsList');
            const items = Array.from(list.querySelectorAll('.time-details-item'));
            
            // 排序项目
            const sortedItems = Array.from(items).sort((a, b) => {
                let valueA, valueB;
                
                if (field === 'name') {
                    // 获取完整显示的名字（可能包含班级号）
                    const nameElementA = a.querySelector('.student-name-link');
                    const nameElementB = b.querySelector('.student-name-link');
                    valueA = nameElementA.textContent.trim();
                    valueB = nameElementB.textContent.trim();
                    
                    // 检查是否是东苑中学的学生（通过红色班级号判断）
                    const isEastSchoolA = nameElementA.querySelector('span[style*="ff4444"]') !== null;
                    const isEastSchoolB = nameElementB.querySelector('span[style*="ff4444"]') !== null;
                    
                    // 提取班级号
                    const classNumA = parseInt(valueA.match(/\d+$/)?.[0] || '0');
                    const classNumB = parseInt(valueB.match(/\d+$/)?.[0] || '0');
                    
                    // 首先按学校分组：滨河 > 东苑 > 其他
                    if (classNumA > 0 || classNumB > 0) {
                        if (isEastSchoolA !== isEastSchoolB) {
                            if (window.sortState.direction === 'asc') {
                                return isEastSchoolA ? 1 : -1;  // 滨河在前，东苑在后
                            } else {
                                return isEastSchoolA ? -1 : 1;  // 东苑在前，滨河在后
                            }
                        }
                        
                        // 同一学校的按班级号排序
                        if (classNumA !== classNumB) {
                            return window.sortState.direction === 'asc' ? 
                                classNumA - classNumB : 
                                classNumB - classNumA;
                        }
                    } else {
                        // 没有班级号的排在最后
                        if ((classNumA > 0) !== (classNumB > 0)) {
                            return window.sortState.direction === 'asc' ?
                                (classNumA > 0 ? -1 : 1) :
                                (classNumA > 0 ? 1 : -1);
                        }
                    }
                    
                    // 如果班级号相同或都没有班级号，按姓名排序
                    const nameA = valueA.replace(/\d+$/, '');
                    const nameB = valueB.replace(/\d+$/, '');
                    return window.sortState.direction === 'asc' ? 
                        nameA.localeCompare(nameB) : 
                        nameB.localeCompare(nameA);
                } else if (field === 'signIn') {
                    valueA = a.querySelector('.time-details-signin').textContent.trim();
                    valueB = b.querySelector('.time-details-signin').textContent.trim();
                } else if (field === 'signOut') {
                    valueA = a.querySelector('.time-details-signout').textContent.trim();
                    valueB = b.querySelector('.time-details-signout').textContent.trim();
                } else if (field === 'points') {  // 新增积分排序逻辑
                    const pointsA = parseInt(a.querySelector('.time-details-points .points-box').textContent.trim()) || 0;
                    const pointsB = parseInt(b.querySelector('.time-details-points .points-box').textContent.trim()) || 0;
                    return window.sortState.direction === 'asc' ? pointsA - pointsB : pointsB - pointsA;
                } else if (field === 'duration') {
                    // 提取时长中的小时和分钟
                    const durationA = a.querySelector('.time-details-duration').textContent.trim();
                    const durationB = b.querySelector('.time-details-duration').textContent.trim();
                    
                    // 如果是 "--"，则转换为0
                    if (durationA === '--') return window.sortState.direction === 'asc' ? -1 : 1;
                    if (durationB === '--') return window.sortState.direction === 'asc' ? 1 : -1;
                    
                    // 解析时长
                    const hoursA = parseInt(durationA.match(/(\d+)小时/)?.[1] || '0');
                    const minutesA = parseInt(durationA.match(/(\d+)分/)?.[1] || '0');
                    const hoursB = parseInt(durationB.match(/(\d+)小时/)?.[1] || '0');
                    const minutesB = parseInt(durationB.match(/(\d+)分/)?.[1] || '0');
                    
                    // 转换为分钟进行比较
                    valueA = hoursA * 60 + minutesA;
                    valueB = hoursB * 60 + minutesB;
                    
                    return window.sortState.direction === 'asc' ? valueA - valueB : valueB - valueA;
                }

                // 对于签到和签退时间的排序
                if (!valueA && valueB) return window.sortState.direction === 'asc' ? 1 : -1;
                if (valueA && !valueB) return window.sortState.direction === 'asc' ? -1 : 1;
                return window.sortState.direction === 'asc' ? 
                    valueA.localeCompare(valueB) : 
                    valueB.localeCompare(valueA);
            });

            // 重新添加排序后的元素
            const header = list.querySelector('.time-details-header');
            list.innerHTML = '';
            list.appendChild(header);
            sortedItems.forEach(item => list.appendChild(item));
        }

        // 添加复制名字列表的函数
        window.copyNames = function() {
            const list = document.getElementById('timeDetailsList');
            const title = document.getElementById('timeDetailsTitle');
            const gradeMatch = title.textContent.match(/(\d+)年级/);
            const currentGrade = gradeMatch ? gradeMatch[1] : null;
            
            // 获取当前显示的名单
            const nameElements = list.querySelectorAll('.student-name-link');
            const displayedNames = Array.from(nameElements).map(el => el.textContent.trim());
            
            // 检查是否已经应用了自定义排序
            if (window.sortState && window.sortState.field !== 'signIn') {
                // 已排序 - 复制排序后的结果
                const textarea = document.createElement('textarea');
                textarea.value = displayedNames.join('\n');
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
                showToast('已复制排序后的学生姓名');
            } else {
                // 未排序或按默认排序 - 使用原始年级列表顺序
                let names = [];
                if (currentGrade && studentsData[currentGrade]) {
                    // 按照原始年级顺序获取名单
                    names = studentsData[currentGrade].map(name => {
                        // 获取学生的班级信息
                        const info = studentDetails[name] || {};
                        const school = info['学校'] || '';
                        const classNum = info['班级'] || '';
                        
                        // 添加班级信息
                        if (school === '滨河实验' || school === '东苑中学') {
                            return `${name}${classNum}`;
                        }
                        return name;
                    });
                    
                    // 创建临时文本区域
                    const textarea = document.createElement('textarea');
                    textarea.value = names.join('\n');
                    document.body.appendChild(textarea);
                    
                    // 选择并复制文本
                    textarea.select();
                    document.execCommand('copy');
                    
                    // 移除临时文本区域
                    document.body.removeChild(textarea);
                    
                    // 显示提示
                    showToast('已复制原始顺序的学生姓名');
                } else {
                    // 如果无法获取原始列表，则复制当前显示的列表
                    const textarea = document.createElement('textarea');
                    textarea.value = displayedNames.join('\n');
                    document.body.appendChild(textarea);
                    textarea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textarea);
                    showToast('已复制学生姓名');
                }
            }
        }

        // 处理模态框中的签到/签退点击
        async function handleModalTimeClick(type) {
            const studentName = document.getElementById('modalStudentName').textContent;
            const timeElement = type === '签到' ? document.getElementById('modalSignInTime') : document.getElementById('modalSignOutTime');
            
            // 检查当前是否已经签到/签退 - 删除已有记录或添加新记录
            const hasRecord = timeElement.textContent !== '未' + type;
            
            // 根据当前状态决定操作类型和确认信息
            const actionType = hasRecord ? '删除' : '添加';
            const confirmMessage = hasRecord 
                ? `是否确认删除学生 ${studentName} 的${type}记录？` 
                : (type === '签退' && !studentDetails[studentName]?.['签到'] 
                    ? '该学生尚未签到，是否确认强制签退？\n不写签退记录，强制给家长发送消息！' 
                    : `是否确认为学生 ${studentName} ${type}？`);
                
            if (!confirm(confirmMessage)) {
                return;
            }

            try {
                // 更新学生信息
                if (!studentDetails[studentName]) {
                    studentDetails[studentName] = {};
                }

                if (hasRecord) {
                    // 删除签到/签退记录
                    // 发送删除请求到后端
                    const response = await fetch(`/api/attendance/delete`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            name: studentName,
                            type: type
                        })
                    });
                    
                    if (!response.ok) {
                        throw new Error(`删除${type}记录失败: ${response.status}`);
                    }
                    
                    // 发送WebSocket消息通知广播
                    sendMessage('cardClick', `删除${type}${studentName}`);
                    
                    // 更新内存中的状态
                    studentDetails[studentName][type] = null;
                    
                    // 更新显示
                    updateTimeStatus(timeElement, null, type);
                    
                    // 显示成功提示
                    showMessage(`${type}记录已删除`, 'success');
                } else {
                    // 添加签到/签退记录 - 原有逻辑
                    const now = new Date();
                    const currentTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;

                    // 检查是否是未签到强制签退的情况
                    if (type === '签退' && !studentDetails[studentName]?.['签到']) {
                        // 发送未签到强制签退的特殊消息
                        sendMessage('cardClick', `未签到强制签退${studentName}`);
                    } else {
                        // 发送普通签到/签退消息
                        sendMessage('cardClick', `强制${type}${studentName}`);
                    }

                    // 更新内存中的签到/签退状态
                    studentDetails[studentName][type] = currentTime;

                    // 更新显示
                    updateTimeStatus(timeElement, currentTime, type);
                    
                    // 显示成功提示
                    if (type === '签退' && !studentDetails[studentName]?.['签到']) {
                        showMessage('已给家长发送签退消息！', 'success');
                    } else {
                        showMessage(type + '成功', 'success');
                    }
                }
                
                // 更新卡片状态
                updateAllCardsStatus();

                // 更新状态栏（根据当前显示的视图）
                if (currentGrade === 'teacher') {
                    updateTeacherStatusBarNew();
                } else if (currentGrade === 'unsign' || currentGrade === 'unsignout') {
                    updateStatusBarAll();
                } else {
                    updateStatusBar(currentGrade);
                }

            } catch (error) {
                console.error(actionType + type + '失败:', error);
                showMessage(actionType + type + '失败，请重试', 'error');
            }
        }

        // 显示搜索模态框
        function showSearchModal() {
            const modal = document.getElementById('searchModal');
            const searchInput = document.getElementById('searchInput');
            const searchButton = document.querySelector('.search-button');
            
            modal.style.display = 'block';
            searchInput.focus();
            searchButton.classList.add('active');
            
            // 当模态框通过关闭按钮关闭时移除active类
            const removeActive = () => {
                searchButton.classList.remove('active');
                const closeBtn = modal.querySelector('.close-modal');
                if (closeBtn) {
                    closeBtn.removeEventListener('click', removeActive);
                }
            };
            
            // 只添加关闭按钮的事件监听
            const closeBtn = modal.querySelector('.close-modal');
            if (closeBtn) {
                closeBtn.addEventListener('click', removeActive);
            }
            
            setupModalEvents(modal);
        }

        // 处理搜索
        function handleSearch() {
            const searchInput = document.getElementById('searchInput');
            const searchResults = document.getElementById('searchResults');
            const query = searchInput.value.trim().toLowerCase();
            
            if (query === '') {
                searchResults.innerHTML = '';
                return;
            }

            // 搜索结果
            const results = [];
            
            // 遍历所有学生
            Object.keys(studentDetails).forEach(name => {
                const pinyin = nameToPinyin[name] || '';
                if (name.toLowerCase().includes(query) || pinyin.toLowerCase().includes(query)) {
                    results.push({
                        name: name,
                        pinyin: pinyin
                    });
                }
            });

            // 显示结果
            if (results.length === 0) {
                searchResults.innerHTML = '<div style="padding: 10px; color: #666;">未找到匹配的学生</div>';
            } else if (results.length === 1) {
                // 如果只有一个结果，直接显示学生信息
                document.getElementById('searchModal').style.display = 'none';
                showStudentDetails(results[0].name);
            } else {
                // 显示多个结果供选择
                searchResults.innerHTML = results.map(result => `
                    <div class="search-result-item" onclick="handleSearchResultClick('${result.name}')">
                        <img src="./FaceID/学生照片/${result.pinyin}.jpg?t=${new Date().getTime()}" 
                             onerror="this.src='./node_modules/默认头像.png'" 
                             alt="${result.name}">
                        <div class="search-result-info">
                            <div class="search-result-name">${result.name}</div>
                            <div class="search-result-pinyin">${result.pinyin}</div>
                        </div>
                    </div>
                `).join('');
            }
        }

        // 处理搜索结果点击
        function handleSearchResultClick(name) {
            const searchButton = document.querySelector('.search-button');
            searchButton.classList.remove('active');
            document.getElementById('searchModal').style.display = 'none';
            showStudentDetails(name);
        }

        // 处理照片选择
        async function handlePhotoUpload(file, studentName, pinyin) {
            try {
                // 检查文件类型
                if (!file.type.startsWith('image/')) {
                    throw new Error('请选择图片文件');
                }

                // 支持的图片格式列表
                const supportedFormats = [
                    'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'tiff', 'svg', 'heic', 'heif'
                ];

                // 获取文件扩展名
                const extension = file.name.split('.').pop().toLowerCase();
                if (!supportedFormats.includes(extension)) {
                    throw new Error('不支持的图片格式，请使用常见图片格式');
                }

                showMessage('正在处理照片...', 'info');

                // 如果文件大于200KB，进行压缩处理
                let processedFile = file;
                if (file.size > 200 * 1024) {
                    processedFile = await processImage(file);
                }

                // 创建新的文件对象，使用工号作为文件名
                const newFileName = `${pinyin}.jpg`;  // 统一使用jpg格式
                const newFile = new File([processedFile], newFileName, { type: 'image/jpeg' });

                const formData = new FormData();
                formData.append('file', newFile);
                formData.append('studentId', pinyin);
                
                showMessage('正在上传照片...', 'info');

                // 上传照片到 FaceID\Picture 目录
                const uploadResponse = await fetch('/upload', {
                    method: 'POST',
                    body: formData
                });

                if (!uploadResponse.ok) {
                    const errorText = await uploadResponse.text();
                    throw new Error(errorText || '照片上传失败');
                }

                showMessage('照片上传成功，正在录入人脸信息...', 'success');

                // 获取学生信息
                const studentCard = document.querySelector(`.student-card[data-name="${studentName}"]`);
                const modalGrade = document.getElementById('modalGrade');
                const grade = modalGrade.value.replace('年级', ''); // 移除"年级"后缀
                const gender = studentCard.getAttribute('data-gender') || '男';

                // 调用Python脚本
                const pythonResponse = await fetch('/python', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        type: 'face',
                        studentName: studentName,
                        pinyin: pinyin,
                        grade: grade, // 使用正确的grade变量
                        gender: gender
                    })
                });

                const result = await pythonResponse.json();
                
                if (result.success) {
                    // 显示人脸录入成功消息
                    showMessage('人脸信息录入成功！', 'success');
                    
                    // 如果人脸录入成功，再次上传照片到学生照片目录
                    try {
                        // 从第一次上传的路径获取图片
                        const imgResponse = await fetch(`/FaceID/Picture/${pinyin}.jpg?t=${new Date().getTime()}`);
                        if (!imgResponse.ok) {
                            throw new Error('获取原始照片失败');
                        }
                        const blob = await imgResponse.blob();
                        const newFile = new File([blob], `${pinyin}.jpg`, { type: 'image/jpeg' });

                        const studentPhotoFormData = new FormData();
                        studentPhotoFormData.append('file', newFile);
                        studentPhotoFormData.append('filename', `${pinyin}.jpg`);
                        studentPhotoFormData.append('directory', 'FaceID/学生照片');  // 使用相对路径
                        studentPhotoFormData.append('studentId', pinyin);

                        const copyResponse = await fetch('/upload', {
                            method: 'POST',
                            body: studentPhotoFormData
                        });

                        if (!copyResponse.ok) {
                            throw new Error(await copyResponse.text());
                        }

                        // 验证新照片是否可访问
                        const verifyResponse = await fetch(`/FaceID/学生照片/${pinyin}.jpg?t=${new Date().getTime()}`);
                        if (!verifyResponse.ok) {
                            throw new Error('新照片验证失败');
                        }

                        // 显示照片复制成功的消息
                        showMessage('照片已同步到学生档案', 'success');
                        
                        // 刷新头像显示
                        const modalAvatar = document.getElementById('modalStudentAvatar');
                        if (modalAvatar) {
                            modalAvatar.src = `/FaceID/学生照片/${pinyin}.jpg?t=${new Date().getTime()}`;
                            modalAvatar.classList.remove('default-avatar');
                        }
                        
                        // 刷新学生卡片上的头像
                        const cardAvatars = document.querySelectorAll(`.student-card[data-name="${studentName}"] .student-avatar img`);
                        cardAvatars.forEach(avatar => {
                            avatar.src = `/FaceID/学生照片/${pinyin}.jpg?t=${new Date().getTime()}`;
                            avatar.classList.remove('default-avatar');
                        });

                        // 强制刷新所有使用该学生头像的元素
                        document.querySelectorAll(`img[src*="${pinyin}.jpg"]`).forEach(img => {
                            img.src = `/FaceID/学生照片/${pinyin}.jpg?t=${new Date().getTime()}`;
                            img.classList.remove('default-avatar');
                        });

                    } catch (error) {
                        console.error('照片处理出错:', error);
                        showMessage('照片处理失败: ' + error.message, 'error');
                        return; // 如果照片处理失败，直接返回不继续执行
                    }

                } else {
                    // 显示详细的错误信息
                    let errorMessage = result.message;
                    if (result.details) {
                        errorMessage += '\n';
                        for (const [school, status] of Object.entries(result.details)) {
                            errorMessage += `\n${school}: ${status}`;
                        }
                    }
                    throw new Error(errorMessage);
                }

            } catch (error) {
                showMessage(error.message, 'error');
                console.error('照片上传错误:', error);
            }
        }

        // 处理图片函数
        async function processImage(file) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = async () => {
                    // 计算2寸照片的尺寸（像素）
                    const targetWidth = 413;   // 35mm = 413px @ 300dpi
                    const targetHeight = 579;  // 49mm = 579px @ 300dpi

                    // 创建canvas
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');

                    // 设置canvas尺寸为目标尺寸
                    canvas.width = targetWidth;
                    canvas.height = targetHeight;

                    // 计算缩放和裁剪参数
                    let sourceWidth = img.width;
                    let sourceHeight = img.height;
                    let sourceX = 0;
                    let sourceY = 0;

                    // 计算源图像需要裁剪的尺寸，保持目标比例
                    const targetRatio = targetWidth / targetHeight;
                    const sourceRatio = sourceWidth / sourceHeight;

                    if (sourceRatio > targetRatio) {
                        // 源图像太宽，需要裁剪宽度
                        sourceWidth = Math.round(sourceHeight * targetRatio);
                        sourceX = Math.round((img.width - sourceWidth) / 2);
                    } else {
                        // 源图像太高，需要裁剪高度
                        sourceHeight = Math.round(sourceWidth / targetRatio);
                        sourceY = Math.round((img.height - sourceHeight) / 2);
                    }

                    // 绘制图片
                    ctx.drawImage(
                        img,
                        sourceX, sourceY,     // 源图像裁剪起始点
                        sourceWidth, sourceHeight,  // 源图像裁剪尺寸
                        0, 0,                 // canvas绘制起始点
                        targetWidth, targetHeight   // canvas绘制尺寸
                    );

                    // 使用二分法找到最佳质量
                    let minQuality = 0.7;  // 最低质量
                    let maxQuality = 1.0;  // 最高质量
                    let bestQuality = 0.9;  // 默认质量
                    let bestBlob = null;

                    while (maxQuality - minQuality > 0.01) {
                        const midQuality = (minQuality + maxQuality) / 2;
                        const blob = await new Promise(resolve => {
                            canvas.toBlob(resolve, 'image/jpeg', midQuality);
                        });

                        if (blob.size <= 200 * 1024) {
                            // 如果大小合适，尝试提高质量
                            bestQuality = midQuality;
                            bestBlob = blob;
                            minQuality = midQuality;
                        } else {
                            // 如果太大，降低质量
                            maxQuality = midQuality;
                        }
                    }

                    // 如果没有找到合适的质量，使用最低质量
                    if (!bestBlob) {
                        bestBlob = await new Promise(resolve => {
                            canvas.toBlob(resolve, 'image/jpeg', minQuality);
                        });
                    }

                    resolve(bestBlob);
                };

                img.onerror = () => {
                    reject(new Error('图片处理失败'));
                };

                img.src = URL.createObjectURL(file);
            });
        }

        // 添加照片上传选项函数
        function showPhotoUploadOptions(studentName, pinyin) {
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'flex';
            modal.style.zIndex = '2000';
            
            modal.innerHTML = `
                <div class="modal-content" style="width: 300px;">
                    <span class="close-modal">&times;</span>
                    <h3 style="margin: 0 0 20px 0; text-align: center;">选择照片上传方式</h3>
                    <div style="display: flex; flex-direction: column; gap: 15px;">
                        <input type="file" id="photoInput" accept="image/jpeg,image/png" capture="user" style="display: none;">
                        <button onclick="document.getElementById('photoInput').click()" style="
                            padding: 12px;
                            background: #1976d2;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 14px;
                            transition: background 0.3s;">
                            拍照
                        </button>
                        <button onclick="document.getElementById('photoInput').removeAttribute('capture'); document.getElementById('photoInput').click()" style="
                            padding: 12px;
                            background: #2e7d32;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 14px;
                            transition: background 0.3s;">
                            从相册选择
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // 处理照片选择
            const photoInput = document.getElementById('photoInput');
            photoInput.onchange = async function(e) {
                const file = e.target.files[0];
                if (file) {
                    modal.remove();  // 关闭选择框
                    await handlePhotoUpload(file, studentName, pinyin);
                }
            };

            // 关闭按钮事件
            const closeBtn = modal.querySelector('.close-modal');
            closeBtn.onclick = () => modal.remove();

            // 点击外部关闭
            modal.onclick = (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            };
        }

        function refreshStudentPhotos(pinyin) {
            // 刷新所有包含该学生照片的img元素
            document.querySelectorAll(`img[src*="${pinyin}.jpg"]`).forEach(img => {
                const timestamp = new Date().getTime();
                img.src = `/FaceID/学生照片/${pinyin}.jpg?t=${timestamp}`;
            });
            
            // 刷新模态框中的照片
            const modalAvatar = document.getElementById('modalStudentAvatar');
            if (modalAvatar && modalAvatar.getAttribute('data-pinyin') === pinyin) {
                modalAvatar.src = `/FaceID/学生照片/${pinyin}.jpg?t=${new Date().getTime()}`;
            }
            
            // 强制浏览器重新加载图片
            const allPhotos = document.querySelectorAll(`img[src*="${pinyin}.jpg"]`);
            allPhotos.forEach(img => {
                const currentSrc = img.src;
                img.src = '';  // 清除src
                img.src = currentSrc;  // 重新设置src
            });
        }

        function updateTimeStatus(element, time, type) {
            if (time) {
                // 有时间记录的情况
                element.textContent = formatTime(time, true);
                if (type === '签退') {
                    element.classList.add('signed-out');
                    element.classList.remove('signed', 'not-signed');
                } else {
                    element.classList.add('signed');
                    element.classList.remove('signed-out', 'not-signed');
                }
                // 修改提示：提示可以删除记录
                element.title = `点击删除${type}记录`;
            } else {
                // 没有时间记录的情况
                element.textContent = `未${type}`;
                if (type === '签到') {
                    // 未签到时显示绿色
                    element.classList.add('signed');
                    element.classList.remove('signed-out', 'not-signed');
                } else {
                    // 未签退时显示红色
                    element.classList.add('not-signed');
                    element.classList.remove('signed', 'signed-out');
                }
                element.title = `点击进行${type}`;
            }
        }

        // 状态栏点击事件已在主DOMContentLoaded中统一绑定

        // 添加全校签到时间汇总显示函数
        function showAllGradesTimeDetails() {
            // 检查是否是管理员登录
            const currentUser = localStorage.getItem('currentUser');
            const isAdmin = currentUser === 'ling';
            
            if (!isAdmin) {
                alert('只有管理员可以查看全校汇总信息');
                return;
            }

            const modal = document.getElementById('timeDetailsModal');
            const title = document.getElementById('timeDetailsTitle');
            const list = document.getElementById('timeDetailsList');
            const modalContent = modal.querySelector('.modal-content'); // 这行是修复
            
            // 获取当前页面状态
            const currentGrade = document.querySelector('.grade-tab.active').getAttribute('data-grade');
            const isUnsignout = currentGrade === 'unsignout';
            
            if (!modal || !title || !list || !modalContent) { // 添加modalContent判断
                console.error('找不到必要的DOM元素');
                return;
            }
            
            // 恢复原始样式
            modalContent.classList.add('time-details-modal');
            modalContent.classList.remove('teacher-time-details-modal');
            
            
            // 清空列表
            list.innerHTML = '';
            
            // 设置标题
            title.textContent = '全校签到时间汇总';
            
            // 添加表头
            const header = document.createElement('div');
            header.className = 'time-details-header';
            header.innerHTML = `
                <span>
                    <svg onclick="copyNames()" viewBox="0 0 24 24" width="16" height="16" style="cursor: pointer; vertical-align: middle; margin-right: 4px;">
                        <path fill="currentColor" d="M16 1H4C2.9 1 2 2 2 3v14h2V3h12V1zm3 4H8C6.9 5 6 5.9 6 7v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                    </svg>
                    <span onclick="sortTimeDetails('name')" style="cursor: pointer;">姓名</span>
                </span>
                <span onclick="sortTimeDetails('points')" style="cursor: pointer;">积分</span>
                <span onclick="sortTimeDetails('signIn')" style="cursor: pointer;">签到时间</span>
                <span onclick="sortTimeDetails('signOut')" style="cursor: pointer;">签退时间</span>
                <span onclick="sortTimeDetails('duration')" style="cursor: pointer;">在校时长</span>
            `;
            list.appendChild(header);
            
            // 添加圆圈年级样式
            const style = document.createElement('style');
            style.textContent = `
                .grade-circle {
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    background-color: #1976d2;
                    color: white;
                    font-size: 12px;
                    margin-right: 8px;
                    flex-shrink: 0;
                }
                .time-details-name {
                    display: flex;
                    align-items: center;
                }
                .student-name-link {
                    display: flex;
                    align-items: center;
                    white-space: nowrap;
                }
            `;
            document.head.appendChild(style);
            
            // 获取所有年级的学生
            let allStudents = [];
            for (let grade = 1; grade <= 9; grade++) {
                const students = studentsData[grade] || [];
                allStudents = allStudents.concat(students.map(name => ({
                    name,
                    grade,
                    info: studentDetails[name] || {}
                })));
            }
            
            // 创建学生签到时间列表
            const timeDetails = allStudents.map(({ name, grade, info }) => {
                const signInTime = info['签到'] ? formatTime(info['签到'], true) : '';
                const signOutTime = info['签退'] ? formatTime(info['签退'], true) : '';
                const duration = calculateDuration(signInTime, signOutTime);
                const school = info['学校'] || '';
                const classNum = info['班级'] || '';
                const points = studentPoints[name] || '0'; // 获取学生积分
                return { 
                    name, 
                    grade,
                    signInTime, 
                    signOutTime, 
                    duration: duration.text,
                    school,
                    classNum,
                    points  // 添加积分字段
                };
            });
            
            // 根据当前页面选择排序方式
            if (isUnsignout) {
                // 在未签退页面，按签退时间从早到晚排序（未签退的排在最后）
                timeDetails.sort((a, b) => {
                    if (!a.signOutTime && !b.signOutTime) return 0;
                    if (!a.signOutTime) return 1;
                    if (!b.signOutTime) return -1;
                    return a.signOutTime.localeCompare(b.signOutTime);
                });
                
                // 更新排序状态
                window.sortState = {
                    field: 'signOut',
                    direction: 'asc'
                };
                
                // 添加排序指示器
                const signOutHeader = header.querySelector('span[onclick="sortTimeDetails(\'signOut\')"]');
                if (signOutHeader) {
                    signOutHeader.classList.add('sort-asc');
                }
            } else {
                // 在未签到页面，保持原有的签到时间排序
                timeDetails.sort((a, b) => {
                    if (!a.signInTime && b.signInTime) return 1;
                    if (a.signInTime && !b.signInTime) return -1;
                    return a.signInTime.localeCompare(b.signInTime);
                });
            }
            
            // 生成HTML
            timeDetails.forEach(({ name, grade, signInTime, signOutTime, duration, school, classNum, points }) => {
                const div = document.createElement('div');
                div.className = 'time-details-item';
                
                // 根据学校显示不同样式的班级信息
                const showClass = school === '滨河实验' || school === '东苑中学';
                let displayName = name;
                if (showClass) {
                    const classStyle = school === '东苑中学' ? 'color: #ff4444;' : 'color: #1976d2;';
                    displayName = `${name}<span style="${classStyle}">${classNum}</span>`;
                }
                
                div.innerHTML = `
                    <span class="time-details-name">
                        <span class="student-name-link" onclick="showStudentDetails('${name}')" 
                              style="cursor: pointer; color: #1976d2; text-decoration: none;">
                            <span class="grade-circle">${grade}</span>
                            ${displayName}
                        </span>
                    </span>
                    <span class="time-details-points">
                        <span class="points-box">${points}</span>
                    </span>
                    <span class="time-details-signin">
                        ${signInTime ? 
                            `<span class="time-box" style="color: #2e7d32; background-color: #e8f5e9">${signInTime}</span>` : 
                            ''}
                    </span>
                    <span class="time-details-signout">
                        ${signOutTime ? 
                            `<span class="time-box" style="color: #ff4444; background-color: #ffebee">${signOutTime}</span>` : 
                            ''}
                    </span>
                    <span class="time-details-duration">
                        ${duration && duration !== '--' ? 
                            `<span class="time-box" style="color: #1976d2; background-color: #e3f2fd">${duration}</span>` : 
                            ''}
                    </span>
                `;
                list.appendChild(div);
            });
            
            // 显示模态框
            modal.style.display = 'block';
        }

        // 修改左下角按钮的显示逻辑
        function updateSummaryButtonVisibility() {
            // 使用更精确的选择器来找到左下角的汇总按钮
            const summaryButton = document.querySelector('[onclick="showAllGradesTimeDetails()"]');
            if (summaryButton) {
                const currentUser = localStorage.getItem('currentUser');
                const isAdmin = currentUser === 'ling';
                
                // 如果不是管理员，隐藏按钮
                if (!isAdmin) {
                    summaryButton.style.display = 'none';
                } else {
                    summaryButton.style.display = 'block';
                }
            }
        }

        // 按钮可见性检查已在主DOMContentLoaded中统一调用

        // 监听登录状态变化
        window.addEventListener('storage', function(e) {
            if (e.key === 'currentUser' || e.key === 'isLoggedIn') {
                updateSummaryButtonVisibility();
            }
        });

        // 在登录和登出时也要更新按钮状态
        function checkLoginStatus() {
            updateSummaryButtonVisibility();
        }

        // 添加教师数据加载和渲染函数
        // refreshWorkId: true=需要重新加载工号信息, false=只刷新考勤数据
        async function loadTeacherData(signInContent, signOutContent, refreshWorkId = false) {

            // 存储全职和兼职老师名单
            let fullTimeTeachersList = new Set();
            let partTimeTeachersList = new Set();

            try {
                // 提取教师相关内容进行比较，而不是整个文件
                const extractTeacherContent = (content) => {
                    if (!content) return '';
                    const lines = content.split('\n');
                    const teacherLines = [];
                    const teacherRegex = /\[(.*?)老师\]/;

                    for (let line of lines) {
                        line = line.trim();
                        if (teacherRegex.test(line) || (teacherLines.length > 0 && line && !line.startsWith('['))) {
                            teacherLines.push(line);
                        }
                    }
                    return teacherLines.join('\n');
                };

                // 只比较教师相关的内容
                const currentTeacherSignIn = extractTeacherContent(signInContent);
                const currentTeacherSignOut = extractTeacherContent(signOutContent);
                const currentTeacherHash = currentTeacherSignIn + '|' + currentTeacherSignOut;

                if (window.lastTeacherContentHash === currentTeacherHash && Object.keys(teacherDetails).length > 0) {
                    // 教师考勤内容没有变化，直接返回无变化
                    return {
                        success: true,
                        hasChanges: false,
                        changedTeachers: [],
                        deletedTeachers: []
                    };
                }

                window.lastTeacherContentHash = currentTeacherHash;

                // 保存当前教师考勤状态用于比较
                const oldTeacherState = {};
                for (const name in teacherDetails) {
                    if (teacherDetails[name]) {
                        oldTeacherState[name] = {
                            签到: teacherDetails[name]['签到'],
                            签退: teacherDetails[name]['签退']
                        };
                    }
                }

                // 不清空现有教师数据，而是重置签到签退状态
                // 这样可以保持教师记录的连续性，避免误判为新教师
                Object.keys(teacherDetails).forEach(key => {
                    if (teacherDetails[key]) {
                        teacherDetails[key]['签到'] = '';
                        teacherDetails[key]['签退'] = '';
                    }
                });
                
                // 添加标准化老师姓名的辅助函数
                function normalizeTeacherName(name) {
                    // 移除末尾的"老师"后缀，确保名字格式统一
                    return name.trim().replace(/老师$/, '');
                }
                
                try {
                    if (signInContent) {
                        // 解析文件内容寻找教师数据
                        const lines = signInContent.split('\n');
                        
                        // 正则表达式匹配形如[张老师]或[李老师]的模式
                        const teacherRegex = /\[(.*?)老师\]/;
                        
                        // 首先解析签到文件，提取教师签到信息
                        let currentTeacher = '';
                        let teacherCount = 0;

                        for (let line of lines) {
                            line = line.trim();
                            const teacherMatch = line.match(teacherRegex);
                            
                            if (teacherMatch) {
                                currentTeacher = teacherMatch[1] + '老师';
                                // 初始化教师数据
                                if (!teacherDetails[currentTeacher]) {
                                    teacherDetails[currentTeacher] = {
                                        '签到': '',
                                        '签退': '',
                                        'workId': '',
                                        'subject': '全科'
                                    };
                                    teacherCount++;
                                }
                            } else if (currentTeacher && teacherDetails[currentTeacher]) {
                                // 处理签到时间
                                if (line.startsWith('上课时间：') || line.startsWith('签到时间=')) {
                                    // 统一处理两种格式的签到时间
                                    let timeStr = line.replace('上课时间：', '').replace('签到时间=', '').trim();
                                    
                                    // 处理可能存在多条签到记录的情况，用|分隔
                                    const times = timeStr.split('|').filter(t => t.trim());
                                    if (times.length > 0) {
                                        // 使用第一条记录作为签到时间（保留首次签到）
                                        timeStr = times[0];
                                        
                                        if (!teacherDetails[currentTeacher]['签到'] || timeStr < teacherDetails[currentTeacher]['签到']) {
                                            teacherDetails[currentTeacher]['签到'] = timeStr;
                                        }
                                    }
                                }
                            }
                        }
                    }
                } catch (error) {
                        console.error('[loadTeacherData] 解析签到文件失败:', error);
                    }
                    

                try {
                    if (signOutContent) {
                        // 解析文件内容寻找教师数据
                        const lines = signOutContent.split('\n');
                        
                        // 正则表达式匹配形如[张老师]或[李老师]的模式
                        const teacherRegex = /\[(.*?)老师\]/;
                        
                        // 首先解析签退文件，提取教师签退信息
                        let currentTeacher = '';
                        let teacherCount = 0;

                        for (let line of lines) {
                            line = line.trim();
                            const teacherMatch = line.match(teacherRegex);
                            
                            if (teacherMatch) {
                                currentTeacher = teacherMatch[1] + '老师';
                                // 初始化教师数据
                                if (!teacherDetails[currentTeacher]) {
                                    teacherDetails[currentTeacher] = {
                                        '签到': '',
                                        '签退': '',
                                        'workId': '',
                                        'subject': '全科'
                                    };
                                    teacherCount++;
                                }
                            } else if (currentTeacher && teacherDetails[currentTeacher]) {
                                // 处理签退时间
                                if (line.startsWith('下课时间：') || line.startsWith('签退时间=')) {
                                    // 统一处理两种格式的签退时间
                                    let timeStr = line.replace('下课时间：', '').replace('签退时间=', '').trim();
                                    
                                    // 处理可能存在多条签退记录的情况，用|分隔
                                    const times = timeStr.split('|').filter(t => t.trim());
                                    if (times.length > 0) {
                                        // 使用最后一条记录作为签退时间
                                        timeStr = times[times.length - 1];
                                        
                                        if (!teacherDetails[currentTeacher]['签退'] || timeStr < teacherDetails[currentTeacher]['签退']) {
                                            teacherDetails[currentTeacher]['签退'] = timeStr;
                                        } else if (teacherDetails[currentTeacher]['签退']) {
                                            //console.log(`[loadTeacherData] ${currentTeacher} 保留现有签退时间 ${teacherDetails[currentTeacher]['签退']}，保留较晚的签退时间 ${timeStr}`);
                                        }
                                    }
                                }
                            }
                        }
                    }
                } catch (error) {
                    console.error('[loadTeacherData] 解析签退文件失败:', error);
                }
                
                // 加载工号信息（只有在需要时才加载）
                if (refreshWorkId || !window.allTeacherWorkIds) {
                    try {
                        const workIdResponse = await fetch('/api/teacher-workid');
                    if (!workIdResponse.ok) {
                        console.error('[loadTeacherData] 无法找到教师工号文件');
                    } else {
                        const workIdContent = await workIdResponse.text();
                        
                        // 解析工号文件
                        const workIdLines = workIdContent.split('\n');
                        
                        // 创建工号映射
                        const workIdMap = {};
                        
                        // 创建两个全局集合，用于存储所有教师信息（不论在职与否）
                        window.allFullTimeTeachers = new Set();
                        window.allPartTimeTeachers = new Set();
                        
                        let currentSection = '';
                        
                        // 解析在职老师名单
                        for (let i = 0; i < workIdLines.length; i++) {
                            const line = workIdLines[i].trim();
                            
                            if (line === '[在职老师]') {
                                currentSection = 'allTeachers';
                                continue;
                            }
                            
                            if (currentSection === 'allTeachers' && line && !line.startsWith('[')) {
                                // 处理在职老师名单，支持INI格式（在职老师=xxx）
                                let teacherListStr = line;
                                
                                // 检查并移除前缀"在职老师="
                                const prefixMatch = line.match(/^在职老师=(.*)$/);
                                if (prefixMatch) {
                                    teacherListStr = prefixMatch[1];
                                }
                                
                                // 处理教师列表，工号-姓名格式，用|分隔
                                const teachers = teacherListStr.split('|');
                                for (const teacherInfo of teachers) {
                                    // 检查是否是工号-姓名格式
                                    const match = teacherInfo.match(/^(\d+)-(.+)$/);
                                    if (match && match[2].trim()) {
                                        let id = match[1].trim();
                                        const name = match[2].trim();
                                        
                                        // 确保工号为4位数
                                        id = id.padStart(4, '0');
                                        
                                        // 忽略工号为1的情况
                                        if (id === '0001' || id === '001' || id === '01' || id === '1' || 
                                            id.replace(/^0+/, '') === '1') {
                                            continue;
                                        }
                                        
                                        const normalizedName = normalizeTeacherName(name);
                                        workIdMap[normalizedName] = id;
                                        
                                        // 根据工号判断是全职还是兼职，同时存入全局集合
                                        if (id.startsWith('00') || id === '0124') {
                                            fullTimeTeachersList.add(normalizedName);
                                            window.allFullTimeTeachers.add(normalizedName);
                                        } else {
                                            partTimeTeachersList.add(normalizedName);
                                            window.allPartTimeTeachers.add(normalizedName);
                                        }
                                    }
                                }
                            }
                            
                            // 如果遇到下一个节段标记，重置当前节段
                            if (line.startsWith('[') && line !== '[在职老师]') {
                                currentSection = '';
                            }
                        }
                        
                        // 新的工号解析逻辑，处理 [工号] 和姓名分开在两行的情况
                        for (let i = 0; i < workIdLines.length - 1; i++) {
                            const line = workIdLines[i].trim();
                            const nextLine = workIdLines[i + 1].trim();
                            
                            // 检查是否是工号行（格式如 [0162]）
                            if (line.match(/^\[\d+\]$/)) {
                                // 提取工号，移除方括号并确保至少4位数字
                                let id = line.replace(/[\[\]]/g, '').trim();
                                // 确保工号为4位数
                                id = id.padStart(4, '0');
                                
                                // 过滤掉工号为1的情况
                                if (id === '0001' || id === '001' || id === '01' || id === '1' || 
                                    id.replace(/^0+/, '') === '1') {
                                    continue;
                                }
                                
                                // 下一行应该是对应的教师姓名
                                if (nextLine && !nextLine.match(/^\[\d+\]$/)) {
                                    const normalizedName = normalizeTeacherName(nextLine);
                                    workIdMap[normalizedName] = id;
                                    
                                    // 将所有解析到的教师添加到全局集合中
                                    if (id.startsWith('00') || id === '0124') {
                                        window.allFullTimeTeachers.add(normalizedName);
                                    } else {
                                        window.allPartTimeTeachers.add(normalizedName);
                                    }
                                }
                            }
                        }
                        
                        // 创建一个所有教师工号的全局映射，供后续使用
                        window.allTeacherWorkIds = workIdMap;

                        // 为教师分配工号 - 修复：将此代码块移到外层循环之外
                        let matchCount = 0;
                        for (const teacherName in teacherDetails) {
                            const normalizedName = normalizeTeacherName(teacherName);
                            if (workIdMap[normalizedName]) {
                                // 获取工号
                                const workId = workIdMap[normalizedName];
                                
                                // 忽略工号1, 01, 001, 0001（不同填充形式的1号工号）
                                if (workId === '0001' || workId === '001' || workId === '01' || workId === '1') {
                                    continue;
                                }
                                
                                teacherDetails[teacherName]['workId'] = workId;
                                matchCount++;
                            }
                        }
                        
                        // 添加未签到的全职和兼职老师
                        // 创建已有教师的标准化名字集合，用于去重
                        const existingNormalizedNames = new Set();
                        for (const teacherName in teacherDetails) {
                            existingNormalizedNames.add(normalizeTeacherName(teacherName));
                        }
                        
                        // 添加全职老师
                        for (const normalizedName of fullTimeTeachersList) {
                            if (!existingNormalizedNames.has(normalizedName)) {
                                // 确认是否有工号
                                const workId = workIdMap[normalizedName] || '';
                                // 忽略工号1的情况
                                if (workId === '0001' || workId === '001' || workId === '01' || workId === '1') {
                                    continue;
                                }
                                
                                // 添加未签到的全职老师，使用标准化名字加"老师"后缀
                                const displayName = normalizedName + '老师';
                                teacherDetails[displayName] = {
                                    '签到': '',
                                    '签退': '',
                                    'workId': workId,
                                    'subject': '全科'
                                };
                                existingNormalizedNames.add(normalizedName);
                            }
                        }
                        
                        // 添加兼职老师
                        for (const normalizedName of partTimeTeachersList) {
                            if (!existingNormalizedNames.has(normalizedName)) {
                                // 确认是否有工号
                                const workId = workIdMap[normalizedName] || '';
                                // 忽略工号1的情况
                                if (workId === '0001' || workId === '001' || workId === '01' || workId === '1') {
                                    continue;
                                }
                                
                                // 添加未签到的兼职老师，使用标准化名字加"老师"后缀
                                const displayName = normalizedName + '老师';
                                teacherDetails[displayName] = {
                                    '签到': '',
                                    '签退': '',
                                    'workId': workId,
                                    'subject': '全科'
                                };
                                existingNormalizedNames.add(normalizedName);
                            }
                        }
                    }
                } catch (error) {
                    console.error('[loadTeacherData] 加载工号信息失败:', error);
                    return { success: false, hasChanges: false, changedTeachers: [], deletedTeachers: [] };
                }

                // 缓存工号信息，避免重复加载
                window.allTeacherWorkIds = window.allTeacherWorkIds || {};
            }

            // 将教师列表提升为全局变量，以便其他模块（如签到查询页面）访问
                // 我们使用 Array.from 将 Set 转换为数组，使其更易于在其他地方使用
                window.fullTimeTeachersList = Array.from(fullTimeTeachersList);
                window.partTimeTeachersList = Array.from(partTimeTeachersList);

                // 渲染教师卡片
                // 直接删除凌世利老师的数据
                for (const teacherName in teacherDetails) {
                    if (teacherName.includes('凌世利')) {
                        delete teacherDetails[teacherName];
                    }
                }
                
                renderTeacherCards();

                // 检测教师考勤变化
                const changedTeachers = [];
                const deletedTeachers = [];

                // 如果是首次加载（没有旧状态），不报告任何变化
                const isFirstDataLoad = Object.keys(oldTeacherState).length === 0;
                if (!isFirstDataLoad) {
                    // 只有非首次加载时才检测变化
                    for (const name in teacherDetails) {
                        if (teacherDetails[name] && oldTeacherState[name]) {
                            const isSignInChanged = teacherDetails[name]['签到'] !== oldTeacherState[name]['签到'];
                            const isSignOutChanged = teacherDetails[name]['签退'] !== oldTeacherState[name]['签退'];

                            if (isSignInChanged || isSignOutChanged) {
                                // 判断是新增还是删除
                                if (teacherDetails[name]['签到'] && !oldTeacherState[name]['签到']) {
                                    changedTeachers.push({ name, type: '签到', action: 'add' });
                                } else if (!teacherDetails[name]['签到'] && oldTeacherState[name]['签到']) {
                                    deletedTeachers.push({ name, type: '签到', action: 'delete' });
                                }

                                if (teacherDetails[name]['签退'] && !oldTeacherState[name]['签退']) {
                                    changedTeachers.push({ name, type: '签退', action: 'add' });
                                } else if (!teacherDetails[name]['签退'] && oldTeacherState[name]['签退']) {
                                    deletedTeachers.push({ name, type: '签退', action: 'delete' });
                                }
                            }
                        } else if (teacherDetails[name] && !oldTeacherState[name]) {
                            // 真正的新教师（之前不存在的教师）
                            // 只有当教师有签到或签退记录时才认为是变化
                            if (teacherDetails[name]['签到']) {
                                changedTeachers.push({ name, type: '签到', action: 'add' });
                            }
                            if (teacherDetails[name]['签退']) {
                                changedTeachers.push({ name, type: '签退', action: 'add' });
                            }
                        }
                    }
                }

                // 二次验证：比较最终的教师状态是否真的有变化
                let actualChanges = false;
                if (!isFirstDataLoad) {
                    for (const name in teacherDetails) {
                        if (teacherDetails[name] && oldTeacherState[name]) {
                            if (teacherDetails[name]['签到'] !== oldTeacherState[name]['签到'] ||
                                teacherDetails[name]['签退'] !== oldTeacherState[name]['签退']) {
                                actualChanges = true;
                                break;
                            }
                        } else if (teacherDetails[name] && !oldTeacherState[name]) {
                            // 真正的新教师
                            if (teacherDetails[name]['签到'] || teacherDetails[name]['签退']) {
                                actualChanges = true;
                                break;
                            }
                        }
                    }
                }

                // 最终决定是否报告变化
                const hasChanges = actualChanges && (changedTeachers.length > 0 || deletedTeachers.length > 0);

                return {
                    success: true,
                    hasChanges: hasChanges,
                    changedTeachers: hasChanges ? changedTeachers : [],
                    deletedTeachers: hasChanges ? deletedTeachers : []
                };
            } catch (error) {
                console.error('[loadTeacherData] 加载教师数据失败:', error);
                return { success: false, hasChanges: false, changedTeachers: [], deletedTeachers: [] };
            }
        }
        
        function renderTeacherCards() {
            // 检查当前是否正在显示教师考勤界面，如果不是则不渲染
            const currentActiveTab = document.querySelector('.grade-tab.active');
            if (currentActiveTab && currentActiveTab.getAttribute('data-grade') !== 'teacher') {
                return; // 不是教师考勤界面，不渲染教师卡片
            }
            
            const container = document.querySelector('#grade-teacher .teacher-cards');
            if (!container) return;
            
            // 如果容器为空，说明是首次渲染或者容器被清空了
            const isFirstRender = container.children.length === 0;
            
            // 收集现有的教师卡片
            const existingCards = {};
            if (!isFirstRender) {
                    container.querySelectorAll('.student-card').forEach(card => {
                    const teacherName = card.getAttribute('data-name');
                    if (teacherName) {
                        existingCards[teacherName] = card;
                        // 有选择地从DOM中临时移除，后续会根据需要重新添加
                        if (!Object.prototype.hasOwnProperty.call(teacherDetails, teacherName)) {
                            // 如果教师不再存在于数据中，则移除卡片
                            card.remove();
                        }
                    }
                });
            }
            
            // 记录已使用的卡片
            const usedCards = new Set();
            
            // 在首次渲染时设置容器样式
            if (isFirstRender) {
                container.style.display = 'flex';
                container.style.flexWrap = 'wrap';
                container.style.flexDirection = 'row';
                container.style.justifyContent = 'flex-start';
                container.style.gap = '15px';
                container.style.padding = '20px';
                
                // 确保父容器不使用网格布局
                const parentContainer = document.getElementById('grade-teacher');
                if (parentContainer) {
                    parentContainer.style.display = 'block';
                }
            }
            
            // 将教师分为全职和兼职两组
            const fullTimeTeachers = {};
            const partTimeTeachers = {};
            
            // 根据工号规则分类教师
            for (const teacherName in teacherDetails) {
                const teacher = teacherDetails[teacherName];
                const workId = teacher.workId || '';
                
                // 跳过工号为1、01、001和0001的教师
                if (workId === '0001' || workId === '001' || workId === '01' || workId === '1') {
                    continue;
                }
                
                // 根据规则分类：00开头的都是全职老师，0124也是全职老师，其他都是兼职老师
                if (workId.startsWith('00') || workId === '0124') {
                    fullTimeTeachers[teacherName] = teacher;
                } else {
                    partTimeTeachers[teacherName] = teacher;
                }
            }
            
            // 获取或创建全职和兼职教师组容器
            let fullTimeGroup = container.querySelector('.teacher-group.full-time');
            let partTimeGroup = container.querySelector('.teacher-group.part-time');
            
            // 如果容器不存在，创建新的
            if (!fullTimeGroup) {
                fullTimeGroup = document.createElement('div');
                fullTimeGroup.className = 'teacher-group full-time';
                container.appendChild(fullTimeGroup);
            } else {
                // 保留组容器，但清空内容
                fullTimeGroup.innerHTML = '';
            }
            
            if (!partTimeGroup) {
                partTimeGroup = document.createElement('div');
                partTimeGroup.className = 'teacher-group part-time';
                container.appendChild(partTimeGroup);
            } else {
                // 保留组容器，但清空内容
                partTimeGroup.innerHTML = '';
            }
            
            // 添加组标题
            const fullTimeTitle = document.createElement('h3');
            fullTimeTitle.innerHTML = `全职老师 <span style="display: inline-block; background-color: #e3f2fd; color: #1976d2; font-size: 14px; padding: 2px 8px; border-radius: 12px; margin-left: 10px;">${Object.keys(fullTimeTeachers).length}人</span>`;
            fullTimeTitle.style.width = '100%';
            fullTimeTitle.style.padding = '10px 0';
            fullTimeTitle.style.borderBottom = '1px solid #ddd';
            fullTimeTitle.style.marginBottom = '15px';
            fullTimeTitle.style.display = 'flex';
            fullTimeTitle.style.alignItems = 'center';
            fullTimeGroup.appendChild(fullTimeTitle);
            
            // 创建全职老师卡片容器
            const fullTimeCards = document.createElement('div');
            fullTimeCards.className = 'teacher-cards-row';
            fullTimeCards.style.display = 'flex';
            fullTimeCards.style.flexWrap = 'wrap';
            fullTimeCards.style.gap = '15px';
            fullTimeCards.style.marginBottom = '20px';
            fullTimeGroup.appendChild(fullTimeCards);
            
            // 添加全职老师卡片，优先使用已存在的卡片
            for (const teacherName in fullTimeTeachers) {
                const teacher = fullTimeTeachers[teacherName];
                let card;
                
                if (existingCards[teacherName]) {
                    // 检查考勤状态是否改变
                    const existingCard = existingCards[teacherName];
                    const hasSignIn = existingCard.querySelector('.check-in-time') !== null;
                    const hasSignOut = existingCard.querySelector('.check-out-time') !== null;
                    const needsUpdate = (hasSignIn !== !!teacher['签到']) || (hasSignOut !== !!teacher['签退']);
                    
                    if (needsUpdate) {
                        // 如果考勤状态有变化，更新现有卡片
                        updateTeacherCard(existingCard, teacherName, teacher);
                    }
                    
                    // 使用现有卡片
                    card = existingCard;
                    usedCards.add(teacherName);
                } else {
                    // 如果卡片不存在，创建新卡片
                    card = createTeacherCard(teacherName, teacher);
                }
                
                fullTimeCards.appendChild(card);
            }
            
            // 添加组标题
            const partTimeTitle = document.createElement('h3');
            partTimeTitle.innerHTML = `兼职老师 <span style="display: inline-block; background-color: #e3f2fd; color: #1976d2; font-size: 14px; padding: 2px 8px; border-radius: 12px; margin-left: 10px;">${Object.keys(partTimeTeachers).length}人</span>`;
            partTimeTitle.style.width = '100%';
            partTimeTitle.style.padding = '10px 0';
            partTimeTitle.style.borderBottom = '1px solid #ddd';
            partTimeTitle.style.marginBottom = '15px';
            partTimeTitle.style.display = 'flex';
            partTimeTitle.style.alignItems = 'center';
            partTimeGroup.appendChild(partTimeTitle);
            
            // 创建兼职老师卡片容器
            const partTimeCards = document.createElement('div');
            partTimeCards.className = 'teacher-cards-row';
            partTimeCards.style.display = 'flex';
            partTimeCards.style.flexWrap = 'wrap';
            partTimeCards.style.gap = '15px';
            partTimeGroup.appendChild(partTimeCards);
            
            // 添加兼职老师卡片，优先使用已存在的卡片
            for (const teacherName in partTimeTeachers) {
                const teacher = partTimeTeachers[teacherName];
                let card;
                
                if (existingCards[teacherName]) {
                    // 检查考勤状态是否改变
                    const existingCard = existingCards[teacherName];
                    const hasSignIn = existingCard.querySelector('.check-in-time') !== null;
                    const hasSignOut = existingCard.querySelector('.check-out-time') !== null;
                    const needsUpdate = (hasSignIn !== !!teacher['签到']) || (hasSignOut !== !!teacher['签退']);
                    
                    if (needsUpdate) {
                        // 如果考勤状态有变化，更新现有卡片
                        updateTeacherCard(existingCard, teacherName, teacher);
                    }
                    
                    // 使用现有卡片
                    card = existingCard;
                    usedCards.add(teacherName);
                } else {
                    // 如果卡片不存在，创建新卡片
                    card = createTeacherCard(teacherName, teacher);
                }
                
                partTimeCards.appendChild(card);
            }
            
            // 确保所有卡片有正确的样式
            document.querySelectorAll('#grade-teacher .student-card').forEach(card => {
                card.style.width = '200px';
                card.style.flex = '0 0 auto';
                card.style.marginBottom = '15px';
                card.style.display = 'inline-block';
            });
        }
        
        // 更新教师卡片内容的辅助函数
        function updateTeacherCard(card, teacherName, teacher) {
            // 更新卡片类名（签到状态）
            if (teacher['签到']) {
                card.classList.add('signed');
            } else {
                card.classList.remove('signed');
            }
            
            // 获取签到和签退状态
            const signInStatus = teacher['签到'] ? teacher['签到'] : '未签到';
            const signOutStatus = teacher['签退'] ? teacher['签退'] : '';  // 未签退时不显示文字
            
            // 确定卡片状态颜色
            let statusColor = '#e0e0e0'; // 默认灰色（未签到）
            if (teacher['签到'] && !teacher['签退']) {
                statusColor = '#4caf50'; // 绿色（已签到未签退）
            } else if (teacher['签到'] && teacher['签退']) {
                statusColor = '#2196f3'; // 蓝色（已签到已签退）
            }
            
            // 更新头像边框颜色
            const avatar = card.querySelector('.student-avatar');
            if (avatar) {
                avatar.style.borderColor = statusColor;
            }
            
            // 更新时间信息
            const timeInfo = card.querySelector('.time-info');
            if (timeInfo) {
                timeInfo.innerHTML = `
                    ${teacher['签到'] ? `<div class="check-in-time">${formatTimeForCard(teacher['签到'])}</div>` : ''}
                    ${teacher['签退'] ? `<div class="check-out-time">${formatTimeForCard(teacher['签退'])}</div>` : ''}
                `;
            }
            
            // 确保工号标签存在并且内容正确
            let workIdBadge = card.querySelector('.workid-badge');
            if (workIdBadge) {
                workIdBadge.textContent = teacher.workId || '未设置';
                } else {
                // 如果工号标签不存在，创建一个
                workIdBadge = document.createElement('div');
                workIdBadge.className = 'workid-badge';
                workIdBadge.title = '工号';
                workIdBadge.textContent = teacher.workId || '未设置';
                card.insertBefore(workIdBadge, card.firstChild);
            }
            
            return card;
        }
        
        // 创建单个教师卡片的辅助函数
        function createTeacherCard(teacherName, teacher) {
            // 创建教师卡片
            const card = document.createElement('div');
            // 如果已签到，添加signed类
            card.className = `student-card${teacher['签到'] ? ' signed' : ''}`;
            card.setAttribute('data-name', teacherName);
            
            // 直接设置卡片样式，确保固定宽度
            card.style.width = '200px';
            card.style.flex = '0 0 auto';
            card.style.marginBottom = '15px';
            card.style.display = 'inline-block';
            
            // 获取签到和签退状态
            const signInStatus = teacher['签到'] ? teacher['签到'] : '未签到';
            const signOutStatus = teacher['签退'] ? teacher['签退'] : '';  // 未签退时不显示文字
            
            // 确定卡片状态颜色
            let statusColor = '#e0e0e0'; // 默认灰色（未签到）
            if (teacher['签到'] && !teacher['签退']) {
                statusColor = '#4caf50'; // 绿色（已签到未签退）
            } else if (teacher['签到'] && teacher['签退']) {
                statusColor = '#2196f3'; // 蓝色（已签到已签退）
            }
            
            // 设置卡片内容
            card.innerHTML = `
                <div class="workid-badge" title="点击查看考勤记录" onclick="event.stopPropagation(); showTeacherAttendanceRecord('${teacherName}', '${teacher.workId || ''}');">${teacher.workId || '未设置'}</div>
                <div class="student-avatar" style="border-color: ${statusColor}">
                    <img src="./FaceID/老师照片/${teacher.workId || 'default'}.jpg" 
                         alt="${teacherName}"
                         onerror="this.src='./node_modules/默认头像.png'"
                         class="${!teacher.workId ? 'default-avatar' : ''}">
                </div>
                <div class="student-name">${teacherName.replace(/老师$/, '')}</div>
                <div class="time-info">
                    ${teacher['签到'] ? `<div class="check-in-time">${formatTimeForCard(teacher['签到'])}</div>` : ''}
                    ${teacher['签退'] ? `<div class="check-out-time">${formatTimeForCard(teacher['签退'])}</div>` : ''}
                </div>
                <div class="school-info">
                    ${teacher.subject || '全科'}
                </div>
                <button class="info-button teacher-photo-upload" aria-label="上传教师照片">
                    <svg viewBox="0 0 24 24" width="20" height="20">
						<path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z"/>
					</svg>
                </button>
            `;
            
            // 添加点击事件处理
            card.addEventListener('click', function() {
                this.classList.toggle('selected');
                
                // 更新显示
                updateSelectedCount();
                
                // 获取信息按钮和工号标签元素
                const infoButton = this.querySelector('.info-button');
                const workIdBadge = this.querySelector('.workid-badge');
                
                // 根据选中状态设置透明度和变换
                if (this.classList.contains('selected')) {
                    // 选中时显示
                            if (infoButton) {
                                infoButton.style.opacity = '1';
                                infoButton.style.transform = 'translateY(0)';
                            }
                    if (workIdBadge) {
                        workIdBadge.style.opacity = '1';
                        workIdBadge.style.transform = 'translateY(0)';
                    }
                } else {
                    // 取消选中时隐藏
                    if (infoButton) {
                        infoButton.style.opacity = '0';
                        infoButton.style.transform = 'translateY(-10px)';
                    }
                    if (workIdBadge) {
                        workIdBadge.style.opacity = '0';
                        workIdBadge.style.transform = 'translateY(-10px)';
                    }
                }
            });

            // 为信息按钮添加点击事件
            const infoButton = card.querySelector('.info-button');
            if (infoButton) {
                infoButton.addEventListener('click', function(e) {
                    e.stopPropagation(); // 阻止事件冒泡到卡片
                    if (!teacher.workId) {
                        showMessage('请先设置教师工号', 'error');
                        return;
                    }
                    showTeacherPhotoUpload(teacherName, teacher.workId);
                });
            }

            return card;
        }

        // 为教师分配工号
        let matchCount = 0;
        for (const teacherName in teacherDetails) {
            if (workIdMap[teacherName]) {
                teacherDetails[teacherName]['workId'] = workIdMap[teacherName];
                matchCount++;
            }
        }
        
        // 添加教师考勤状态栏更新函数
        function updateTeacherStatusBarNew() {
            
            const statusGrade = document.querySelector('.status-grade');
            const statusTotal = document.querySelector('.status-total');
            const statusSigned = document.querySelector('.status-signed');
            const statusUnsigned = document.querySelector('.status-unsigned');
            const statusLeave = document.querySelector('.status-leave');

            if (!statusGrade || !statusTotal || !statusSigned || !statusUnsigned || !statusLeave) {
                console.error('[updateTeacherStatusBarNew] 状态栏元素未找到');
                return;
            }

            // 计算教师统计信息
            let totalCount = 0;
            let signedCount = 0;
            
            // 遍历所有教师
            for (const teacherName in teacherDetails) {
                const teacher = teacherDetails[teacherName];
                const workId = teacher.workId || '';
                
                // 跳过工号为1、01、001和0001的教师
                if (workId === '0001' || workId === '001' || workId === '01' || workId === '1') {
                    continue;
                }
                
                totalCount++;
                if (teacher['签到']) {
                    signedCount++;
                }
            }
            
            const unsignedCount = totalCount - signedCount;

            // 更新统计信息
            statusGrade.textContent = '教师考勤';
            statusTotal.textContent = `总人数：${totalCount}人`;
            statusSigned.textContent = `已签到：${signedCount}人`;
            statusUnsigned.textContent = `未签到：${unsignedCount}人`;
            statusLeave.textContent = `请假：0人`;

        }

        // 添加显示教师考勤详情的函数
        function showTeacherTimeDetails() {

            const modal = document.getElementById('timeDetailsModal');
            const title = document.getElementById('timeDetailsTitle');
            const list = document.getElementById('timeDetailsList');
            const modalContent = modal.querySelector('.modal-content');
            
            if (!modal || !title || !list || !modalContent) {
                console.error('[showTeacherTimeDetails] 找不到必要的DOM元素');
                return;
            }
            
            // 添加教师考勤专用的宽度类
            modalContent.classList.add('teacher-time-details-modal');
            modalContent.classList.remove('time-details-modal');
            
            // 清空列表
            list.innerHTML = '';
            
            // 设置标题
            title.textContent = '教师考勤统计详情';
            
            // 初始化排序状态
            window.teacherSortState = {
                field: 'signIn',
                direction: 'asc'
            };
            
            // 添加表头
            const header = document.createElement('div');
            header.className = 'time-details-header teacher-header';
            header.innerHTML = `
                <span>
                    <svg onclick="copyTeacherNames()" viewBox="0 0 24 24" width="16" height="16" style="cursor: pointer; vertical-align: middle; margin-right: 4px;">
                        <path fill="currentColor" d="M16 1H4C2.9 1 2 1.9 2 3v14h2V3h12V1zm3 4H8C6.9 5 6 5.9 6 7v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                    </svg>
                    <span onclick="sortTeacherTimeDetails('name')" style="cursor: pointer;">姓名</span>
                </span>
                <span onclick="sortTeacherTimeDetails('workId')" style="cursor: pointer;">工号</span>
                <span onclick="sortTeacherTimeDetails('subject')" style="cursor: pointer;">科目</span>
                <span onclick="sortTeacherTimeDetails('signIn')" style="cursor: pointer;">签到时间</span>
                <span onclick="sortTeacherTimeDetails('signOut')" style="cursor: pointer;">签退时间</span>
                <span onclick="sortTeacherTimeDetails('duration')" style="cursor: pointer;">在校时长</span>
            `;
            list.appendChild(header);
            
            // 创建两个数组分别存储全职和兼职教师
            const fullTimeTeachers = [];
            const partTimeTeachers = [];
            
            // 分类教师
            for (const teacherName in teacherDetails) {
                const teacher = teacherDetails[teacherName];
                const workId = teacher.workId || '';
                
                // 跳过工号为1、01、001和0001的教师
                if (workId === '0001' || workId === '001' || workId === '01' || workId === '1') {
                    continue;
                }
                
                // 添加教师名称属性
                teacher.name = teacherName;
                
                // 根据规则分类：00开头的都是全职老师，0124也是全职老师，其他都是兼职老师
                if (workId.startsWith('00') || workId === '0124') {
                    fullTimeTeachers.push(teacher);
                } else {
                    partTimeTeachers.push(teacher);
                }
            }
            
            // 添加全职教师组标题
            const fullTimeTitle = document.createElement('div');
            fullTimeTitle.className = 'time-details-group-title';
            fullTimeTitle.innerHTML = `全职教师 <span style="display: inline-block; background-color: #e3f2fd; color: #1976d2; font-size: 14px; padding: 2px 8px; border-radius: 12px; margin-left: 10px;">${fullTimeTeachers.length}人</span>`;
            fullTimeTitle.style.fontWeight = 'bold';
            fullTimeTitle.style.padding = '10px 0';
            fullTimeTitle.style.borderBottom = '1px solid #ddd';
            fullTimeTitle.style.marginTop = '15px';
            fullTimeTitle.style.display = 'flex';
            fullTimeTitle.style.alignItems = 'center';
            list.appendChild(fullTimeTitle);
            
            // 添加全职教师列表
            addTeacherTimeList(fullTimeTeachers, list);
            
            // 添加兼职教师组标题
            const partTimeTitle = document.createElement('div');
            partTimeTitle.className = 'time-details-group-title';
            partTimeTitle.innerHTML = `兼职教师 <span style="display: inline-block; background-color: #e3f2fd; color: #1976d2; font-size: 14px; padding: 2px 8px; border-radius: 12px; margin-left: 10px;">${partTimeTeachers.length}人</span>`;
            partTimeTitle.style.fontWeight = 'bold';
            partTimeTitle.style.padding = '10px 0';
            partTimeTitle.style.borderBottom = '1px solid #ddd';
            partTimeTitle.style.marginTop = '15px';
            partTimeTitle.style.display = 'flex';
            partTimeTitle.style.alignItems = 'center';
            list.appendChild(partTimeTitle);
            
            // 添加兼职教师列表
            addTeacherTimeList(partTimeTeachers, list);
            
            // 显示模态框
            modal.style.display = 'block';

            // 设置默认排序并显示排序箭头
            const signInHeader = header.querySelector('span[onclick="sortTeacherTimeDetails(\'signIn\')"]');
            if (signInHeader) {
                signInHeader.classList.add('sort-asc');
            }
        }
        
        // 添加教师考勤列表的辅助函数
        function addTeacherTimeList(teachers, list) {
            teachers.forEach(teacher => {
                const signInTime = teacher['签到'] ? formatTime(teacher['签到'], true) : '--:--:--';
                const signOutTime = teacher['签退'] ? formatTime(teacher['签退'], true) : '--:--:--';
                const duration = calculateDuration(signInTime, signOutTime).text;
                
                const div = document.createElement('div');
                div.className = 'time-details-item teacher-item';
                div.innerHTML = `
                    <span class="time-details-name"><span style="color: #1976d2;">${teacher.name.replace(/老师$/, '')}</span></span>
                    <span class="time-details-workid">${teacher.workId || '--'}</span>
                    <span class="time-details-subject">${teacher.subject || '--'}</span>
                    <span class="time-details-signin">
                        ${signInTime !== '--:--:--' ? 
                            `<span class="time-box" style="color: #4caf50; background-color: #e8f5e9">${signInTime}</span>` : 
                            `<span class="time-box" style="display: none;"></span>`}
                    </span>
                    <span class="time-details-signout">
                        ${signOutTime !== '--:--:--' ? 
                            `<span class="time-box" style="color: #ff4444; background-color: #ffebee">${signOutTime}</span>` : 
                            `<span class="time-box" style="display: none;"></span>`}
                    </span>
                    <span class="time-details-duration">
                        ${duration && duration !== '--' ? 
                            `<span class="time-box" style="color: #1976d2; background-color: #e3f2fd">${duration}</span>` : 
                            `<span class="time-box" style="display: none;"></span>`}
                    </span>
                `;
                list.appendChild(div);
            });
        }
        
        // 添加复制教师名单的函数
        function copyTeacherNames() {
            const fullTimeTeachers = [];
            const partTimeTeachers = [];
            
            // 分类教师
            for (const teacherName in teacherDetails) {
                const teacher = teacherDetails[teacherName];
                const workId = teacher.workId || '';
                
                // 跳过工号为1、01、001和0001的教师
                if (workId === '0001' || workId === '001' || workId === '01' || workId === '1') {
                    continue;
                }
                
                const displayName = teacherName.replace(/老师$/, '');
                
                // 根据规则分类
                if (workId.startsWith('00') || workId === '0124') {
                    fullTimeTeachers.push(`${displayName}(${workId})`);
                } else {
                    partTimeTeachers.push(`${displayName}(${workId})`);
                }
            }
            
            // 组合名单
            const nameList = 
                '全职教师：\n' + fullTimeTeachers.join('\n') + 
                '\n\n兼职教师：\n' + partTimeTeachers.join('\n');
            
            // 添加剪贴板API的兼容性检查
            if (navigator.clipboard && navigator.clipboard.writeText) {
                // 现代浏览器支持的方法
                navigator.clipboard.writeText(nameList)
                    .then(() => {
                        alert('教师名单已复制到剪贴板');
                    })
                    .catch(err => {
                        console.error('复制失败:', err);
                        fallbackCopy(nameList);
                    });
            } else {
                // 不支持clipboard API时的备用方案
                fallbackCopy(nameList);
            }
        }

        // 备用复制方法
        function fallbackCopy(text) {
            // 创建临时文本区域
            const textarea = document.createElement('textarea');
            textarea.value = text;
            textarea.style.position = 'fixed';  // 避免滚动页面
            document.body.appendChild(textarea);
            textarea.select();
            
            try {
                // 执行复制命令
                const successful = document.execCommand('copy');
                if (successful) {
					showToast('教师名单已复制到剪贴板');
                } else {
                    alert('复制失败，请手动复制下面的内容：\n\n' + text);
                }
            } catch (err) {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制下面的内容：\n\n' + text);
            }
            
            // 清理临时元素
            document.body.removeChild(textarea);
        }
        
        // 添加教师考勤排序函数
        function sortTeacherTimeDetails(field) {
            
            // 获取全职和兼职教师组标题元素
            const groupTitles = document.querySelectorAll('.time-details-group-title');
            if (groupTitles.length !== 2) {
                console.error('[sortTeacherTimeDetails] 未找到教师组标题元素');
                return;
            }

            // 清除所有排序状态
            const nameHeader = document.querySelector('.time-details-header.teacher-header span > span');
            const otherHeaders = document.querySelectorAll('.time-details-header.teacher-header > span:not(:first-child)');
            
            // 清除所有排序状态
            if (nameHeader) nameHeader.classList.remove('sort-asc', 'sort-desc');
            otherHeaders.forEach(header => {
                header.classList.remove('sort-asc', 'sort-desc');
            });

            // 更新排序方向
            if (window.teacherSortState && window.teacherSortState.field === field) {
                window.teacherSortState.direction = window.teacherSortState.direction === 'asc' ? 'desc' : 'asc';
            } else {
                window.teacherSortState = {
                    field: field,
                    direction: 'asc'
                };
            }
            
            // 更新排序箭头
            if (field === 'name') {
                if (nameHeader) {
                    nameHeader.classList.add(`sort-${window.teacherSortState.direction}`);
                } else {
                    console.error('[sortTeacherTimeDetails] 找不到姓名标头');
                }
            } else {
                const columnMap = {
                    'workId': 0,
                    'subject': 1,
                    'signIn': 2,
                    'signOut': 3,
                    'duration': 4
                };
                const columnIndex = columnMap[field];
                if (columnIndex !== undefined && otherHeaders[columnIndex]) {
                    otherHeaders[columnIndex].classList.add(`sort-${window.teacherSortState.direction}`);
                } else {
                    console.error(`[sortTeacherTimeDetails] 找不到${field}标头, 索引=${columnIndex}, 存在=${!!otherHeaders[columnIndex]}`);
                }
            }
            
            const fullTimeTitle = groupTitles[0];
            const partTimeTitle = groupTitles[1];
            
            // 获取全职教师列表项
            const fullTimeItems = [];
            let el = fullTimeTitle.nextElementSibling;
            while (el && !el.classList.contains('time-details-group-title')) {
                if (el.classList.contains('time-details-item')) {
                    fullTimeItems.push(el);
                }
                el = el.nextElementSibling;
            }
            
            // 获取兼职教师列表项
            const partTimeItems = [];
            el = partTimeTitle.nextElementSibling;
            while (el) {
                if (el.classList.contains('time-details-item')) {
                    partTimeItems.push(el);
                }
                el = el.nextElementSibling;
            }
            
            // 定义排序函数
            function sortItems(items, field) {
                const getFieldValue = (item) => {
                    switch (field) {
                        case 'name':
                            return item.querySelector('.time-details-name').textContent;
                        case 'workId':
                            return item.querySelector('.time-details-workid').textContent;
                        case 'subject':
                            return item.querySelector('.time-details-subject').textContent;
                        case 'signIn': {
                            const signInBox = item.querySelector('.time-details-signin .time-box');
                            if (!signInBox) {
                                return 'z'; // 处理空元素情况
                            }
                            const signInText = signInBox.textContent;
                            return signInText === '未签到' || signInText === '' ? 'z' : signInText; // 未签到排在最后
                        }
                        case 'signOut': {
                            const signOutBox = item.querySelector('.time-details-signout .time-box');
                            if (!signOutBox) {
                                return 'z'; // 处理空元素情况
                            }
                            const signOutText = signOutBox.textContent;
                            return signOutText === '未签退' || signOutText === '' ? 'z' : signOutText; // 未签退排在最后
                        }
                        case 'duration':
                            const durationEl = item.querySelector('.time-details-duration .time-box');
                            return durationEl ? durationEl.textContent : '';
                        default:
                            return '';
                    }
                };

                items.sort((a, b) => {
                    const aValue = getFieldValue(a);
                    const bValue = getFieldValue(b);
                    
                    // 改进时间比较逻辑
                    if (field === 'signIn' || field === 'signOut') {
                        // 如果是时间字段，需要特殊处理
                        if (aValue === 'z' && bValue === 'z') return 0;
                        if (aValue === 'z') return 1; // 空值排在后面
                        if (bValue === 'z') return -1;
                        
                        // 比较时间
                        return window.teacherSortState.direction === 'asc' ? 
                            aValue.localeCompare(bValue) : 
                            bValue.localeCompare(aValue);
                    } else if (field === 'duration') {
                        // 持续时间需要解析数字进行比较
                        // 预期格式：'1小时30分' 或 '--'
                        if (aValue === '' && bValue === '') return 0;
                        if (aValue === '') return 1;
                        if (bValue === '') return -1;
                        
                        // 转换为分钟计算
                        const getMinutes = (str) => {
                            if (str === '--') return 0;
                            const hours = parseInt(str.match(/(\d+)小时/)?.[1] || '0');
                            const minutes = parseInt(str.match(/(\d+)分/)?.[1] || '0');
                            return hours * 60 + minutes;
                        };
                        
                        const aMinutes = getMinutes(aValue);
                        const bMinutes = getMinutes(bValue);
                        
                        return window.teacherSortState.direction === 'asc' ? 
                            aMinutes - bMinutes : 
                            bMinutes - aMinutes;
                } else {
                        // 其他字段使用字符串比较
                        return window.teacherSortState.direction === 'asc' ? 
                            aValue.localeCompare(bValue) : 
                            bValue.localeCompare(aValue);
                    }
                });
                
                // 从DOM中移除所有项
                items.forEach(item => item.remove());
                
                return items;
            }
            
            // 排序全职教师
            const sortedFullTimeItems = sortItems(fullTimeItems, field);
            
            // 排序兼职教师
            const sortedPartTimeItems = sortItems(partTimeItems, field);
            
            // 重新添加到DOM
            const list = document.getElementById('timeDetailsList');
            
            // 重建全职教师组标题（使用最新的排序后人数）
            const fullTimeTitleNew = document.createElement('div');
            fullTimeTitleNew.className = 'time-details-group-title';
            fullTimeTitleNew.innerHTML = `全职教师 <span style="display: inline-block; background-color: #e3f2fd; color: #1976d2; font-size: 14px; padding: 2px 8px; border-radius: 12px; margin-left: 10px;">${sortedFullTimeItems.length}人</span>`;
            fullTimeTitleNew.style.fontWeight = 'bold';
            fullTimeTitleNew.style.padding = '10px 0';
            fullTimeTitleNew.style.borderBottom = '1px solid #ddd';
            fullTimeTitleNew.style.marginTop = '15px';
            fullTimeTitleNew.style.display = 'flex';
            fullTimeTitleNew.style.alignItems = 'center';
            fullTimeTitle.remove();
            list.appendChild(fullTimeTitleNew);

            // 添加排序后的全职教师
            sortedFullTimeItems.forEach(item => list.appendChild(item));

            // 重建兼职教师组标题（使用最新的排序后人数）
            const partTimeTitleNew = document.createElement('div');
            partTimeTitleNew.className = 'time-details-group-title';
            partTimeTitleNew.innerHTML = `兼职教师 <span style="display: inline-block; background-color: #e3f2fd; color: #1976d2; font-size: 14px; padding: 2px 8px; border-radius: 12px; margin-left: 10px;">${sortedPartTimeItems.length}人</span>`;
            partTimeTitleNew.style.fontWeight = 'bold';
            partTimeTitleNew.style.padding = '10px 0';
            partTimeTitleNew.style.borderBottom = '1px solid #ddd';
            partTimeTitleNew.style.marginTop = '15px';
            partTimeTitleNew.style.display = 'flex';
            partTimeTitleNew.style.alignItems = 'center';
            partTimeTitle.remove();
            list.appendChild(partTimeTitleNew);

            // 添加排序后的兼职教师
            sortedPartTimeItems.forEach(item => list.appendChild(item));
        }

        // 状态栏点击事件已在主DOMContentLoaded中统一绑定

        // 处理教师照片上传选项显示
        function showTeacherPhotoUpload(teacherName, workId) {
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'flex';
            modal.style.zIndex = '2000';
            
            modal.innerHTML = `
                <div class="modal-content" style="width: 300px;">
                    <span class="close-modal">&times;</span>
                    <h3 style="margin: 0 0 20px 0; text-align: center;">教师照片上传</h3>
                    <div style="display: flex; flex-direction: column; gap: 15px;">
                        <input type="file" id="teacherPhotoInput" accept="image/jpeg,image/png" capture="user" style="display: none;">
                        <button onclick="document.getElementById('teacherPhotoInput').click()" style="
                            padding: 12px;
                            background: #1976d2;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 14px;
                            transition: background 0.3s;">
                            拍照
                        </button>
                        <button onclick="document.getElementById('teacherPhotoInput').removeAttribute('capture'); document.getElementById('teacherPhotoInput').click()" style="
                            padding: 12px;
                            background: #2e7d32;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 14px;
                            transition: background 0.3s;">
                            从相册选择
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // 处理照片选择
            const photoInput = document.getElementById('teacherPhotoInput');
            photoInput.onchange = async function(e) {
                const file = e.target.files[0];
                if (file) {
                    modal.remove();  // 关闭选择框
                    await handleTeacherPhotoUpload(file, teacherName, workId);
                }
            };

            // 关闭按钮事件
            const closeBtn = modal.querySelector('.close-modal');
            closeBtn.onclick = () => modal.remove();

            // 点击外部关闭
            modal.onclick = (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            };
        }

        // 处理教师照片上传
        async function handleTeacherPhotoUpload(file, teacherName, workId) {
            try {
                // 检查文件类型
                if (!file.type.startsWith('image/')) {
                    throw new Error('请选择图片文件');
                }

                // 支持的图片格式列表
                const supportedFormats = [
                    'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'tiff', 'svg', 'heic', 'heif'
                ];

                // 获取文件扩展名
                const extension = file.name.split('.').pop().toLowerCase();
                if (!supportedFormats.includes(extension)) {
                    throw new Error('不支持的图片格式，请使用常见图片格式');
                }

                showToast('正在处理照片...', 'info');

                // 如果文件大于200KB，进行压缩处理
                let processedFile = file;
                if (file.size > 200 * 1024) {
                    processedFile = await processImage(file);
                }

                // 创建新的文件对象，使用工号作为文件名
                const newFileName = `${workId}.jpg`;  // 统一使用jpg格式
                const newFile = new File([processedFile], newFileName, { type: 'image/jpeg' });

                const formData = new FormData();
                formData.append('file', newFile);
                formData.append('filename', newFileName);
                formData.append('directory', 'FaceID/Picture');  // 先上传到Picture目录
                formData.append('workId', workId);
                
                showToast('正在上传照片...', 'info');

                // 上传到FaceID/Picture目录
                const uploadResponse = await fetch('/upload', {
                    method: 'POST',
                    body: formData
                });

                if (!uploadResponse.ok) {
                    const errorText = await uploadResponse.text();
                    throw new Error(errorText || '照片上传失败');
                }

                showToast('照片上传成功，正在录入人脸信息...', 'success');

                // 判断教师类型：全职或兼职
                let teacherType = '';
                if (workId.startsWith('00') || workId === '0124') {
                    teacherType = '全职';
                } else {
                    teacherType = '兼职';
                }

                // 调用Python脚本进行人脸录入
                const pythonResponse = await fetch('/python', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        type: 'face',
                        studentName: teacherName.replace(/老师$/, ''),  // 去除名称末尾可能的"老师"字样
                        pinyin: workId,            // 使用工号替代拼音
                        grade: teacherType,        // 使用教师类型替代年级
                        gender: '女'              // 标记为教师
                    })
                });

                const result = await pythonResponse.json();
                
                if (result.success) {
                    // 显示人脸录入成功消息
                    showToast('教师人脸信息录入成功！', 'success');
                    
                    // 如果人脸录入成功，再次上传照片到老师照片目录
                    try {
                        // 从第一次上传的路径获取图片
                        const imgResponse = await fetch(`/FaceID/Picture/${workId}.jpg?t=${new Date().getTime()}`);
                        if (!imgResponse.ok) {
                            throw new Error('获取原始照片失败');
                        }
                        const blob = await imgResponse.blob();
                        const newFile = new File([blob], `${workId}.jpg`, { type: 'image/jpeg' });

                        const teacherPhotoFormData = new FormData();
                        teacherPhotoFormData.append('file', newFile);
                        teacherPhotoFormData.append('filename', `${workId}.jpg`);
                        teacherPhotoFormData.append('directory', 'FaceID/老师照片');  // 使用相对路径
                        teacherPhotoFormData.append('workId', workId);

                        const copyResponse = await fetch('/upload', {
                            method: 'POST',
                            body: teacherPhotoFormData
                        });

                        if (!copyResponse.ok) {
                            throw new Error(await copyResponse.text());
                        }

                        // 强制刷新所有使用该教师头像的元素
                        const imgElements = document.querySelectorAll(`img[src*="${workId}.jpg"]`);
                        imgElements.forEach(img => {
                            img.src = `./FaceID/老师照片/${workId}.jpg?t=${new Date().getTime()}`;
                            img.classList.remove('default-avatar');
                        });
                        
                        // 刷新使用默认头像的教师卡片
                        const teacherCards = document.querySelectorAll(`.student-card[data-name="${teacherName}"]`);
                        teacherCards.forEach(card => {
                            const avatarImgs = card.querySelectorAll('.student-avatar img');
                            
                            avatarImgs.forEach(img => {
                                // 无论是否有default-avatar类都进行更新
                                const newSrc = `./FaceID/老师照片/${workId}.jpg?t=${new Date().getTime()}`;
                                img.src = newSrc;
                                
                                if (img.classList.contains('default-avatar')) {
                                    img.classList.remove('default-avatar');
                                }
                            });
                        });
                    } catch (error) {
                        showToast('照片已录入但同步到教师档案失败: ' + error.message, 'warning');
                        console.error('复制教师照片错误:', error);
                    }
                } else {
                    throw new Error(result.message || '人脸录入失败');
                }
            } catch (error) {
                showToast(error.message, 'error');
                console.error('教师照片上传错误:', error);
            }
        }

        // 直接更新所有卡片的请假状态
        function updateCardLeaveStatus() {
            // 更新未签到视图中的卡片
            const unsignContainer = document.querySelector('.unsign-summary');
            if (unsignContainer) {
                unsignContainer.querySelectorAll('.student-card').forEach(card => {
                    const name = card.getAttribute('data-name');
                    if (name) {
                        // 更新请假状态
                        if (leaveStudents.has(name)) {
                            card.classList.add('on-leave');
                            // 确保请假标记存在
                            if (!card.querySelector('.leave-badge')) {
                                const leaveBadge = document.createElement('div');
                                leaveBadge.className = 'leave-badge';
                                leaveBadge.textContent = '假';
                                card.appendChild(leaveBadge);
                            }
                        } else {
                            card.classList.remove('on-leave');
                            // 移除请假标记
                            const leaveBadge = card.querySelector('.leave-badge');
                            if (leaveBadge) {
                                leaveBadge.remove();
                            }
                        }
                    }
                });
            }
            
            // 更新未签退视图中的卡片
            const unsignoutContainer = document.querySelector('#grade-unsignout .unsign-summary');
            if (unsignoutContainer) {
                unsignoutContainer.querySelectorAll('.student-card').forEach(card => {
                    const name = card.getAttribute('data-name');
                    if (name) {
                        // 更新请假状态
                        if (leaveStudents.has(name)) {
                            card.classList.add('on-leave');
                            // 确保请假标记存在
                            if (!card.querySelector('.leave-badge')) {
                                const leaveBadge = document.createElement('div');
                                leaveBadge.className = 'leave-badge';
                                leaveBadge.textContent = '假';
                                card.appendChild(leaveBadge);
                            }
                        } else {
                            card.classList.remove('on-leave');
                            // 移除请假标记
                            const leaveBadge = card.querySelector('.leave-badge');
                            if (leaveBadge) {
                                leaveBadge.remove();
                            }
                        }
                    }
                });
            }
        }
    </script>

    <!-- 检查登录状态 -->
    <script>
        // 检查登录状态
        if (localStorage.getItem('isLoggedIn') !== 'true') {
            window.location.href = 'login.html';
        }
    </script>

    <!-- 添加通用的模态框事件处理函数 -->
    <script>
        function setupModalEvents(modal) {
            const searchButton = document.querySelector('.search-button');
            
            // 只针对非搜索模态框应用点击外部关闭的逻辑
            if (modal.id !== 'searchModal') {
                // 点击模态框外部关闭
                modal.onclick = function(event) {
                    if (event.target === modal) {
                        modal.style.display = 'none';
                    }
                };
            }

            // 阻止模态框内容的点击事件冒泡
            const modalContent = modal.querySelector('.modal-content');
            modalContent.onclick = function(event) {
                event.stopPropagation();
            };

            // 关闭按钮点击事件
            const closeBtn = modal.querySelector('.close-modal');
            if (closeBtn) {
                closeBtn.onclick = () => {
                    modal.style.display = 'none';
                    if (modal.id === 'searchModal') {
                        searchButton.classList.remove('active');
                    }
                };
            }
        }
    </script>

    <!-- 初始化教师照片上传相关的样式 -->
    <script>
        function initTeacherPhotoStyles() {
            // 创建样式元素
            const styleElement = document.createElement('style');
            
            // 设置样式内容
            styleElement.innerHTML = 
                '.student-avatar img { cursor: pointer !important; }' +
                '.student-avatar img:hover { opacity: 0.8; }' +
                '.student-avatar img.default-avatar { position: relative; }' +
                '.student-avatar img.default-avatar:hover::before {' +
                '    content: "点击上传照片";' +
                '    position: absolute;' +
                '    top: 50%;' +
                '    left: 50%;' +
                '    transform: translate(-50%, -50%);' +
                '    background: rgba(0, 0, 0, 0.7);' +
                '    color: white;' +
                '    font-size: 12px;' +
                '    padding: 4px 8px;' +
                '    border-radius: 4px;' +
                '    white-space: nowrap;' +
                '    z-index: 2;' +
                '}';
            
            // 添加到文档头部
            document.head.appendChild(styleElement);
        }

        // 教师照片样式初始化已在主DOMContentLoaded中统一调用
    </script>
    
    <!-- 添加搜索所有学生功能 -->
    <script src="node_modules/search-all.js"></script>
    
    <!-- 添加显示教师签到记录查询的功能 -->
    <script>
        // 显示教师签到记录查询函数
        function showTeacherAttendanceRecord(teacherName, workId) {
            // 确保教师名字后面带有"老师"后缀
            let fullTeacherName = teacherName;
            if (!teacherName.endsWith('老师')) {
                fullTeacherName = teacherName + '老师';
            }
            
            // 显示签到记录模态框
            const attendanceModal = document.getElementById('attendanceModal');
            if (attendanceModal) {
            // 显示模态框
                attendanceModal.style.display = 'block';
                
                // 初始化查询界面
                if (window.AttendanceQuery) {
                    window.AttendanceQuery.initAttendanceQuery();
                    
                    // 自动填入教师姓名并执行查询
                    const studentNameInput = document.getElementById('student-name-input');
                    if (studentNameInput) {
                        studentNameInput.value = fullTeacherName;
                        
                        // 执行查询
                        //window.AttendanceQuery.queryAttendanceRecord(fullTeacherName);
                    }
                }
                
                // 绑定关闭事件
                const closeButton = attendanceModal.querySelector('.close-modal');
                if (closeButton) {
                    closeButton.onclick = function() {
                        attendanceModal.style.display = 'none';
                    };
                }
            }
        }
    </script>
    
    <!-- 添加签到记录查询功能 -->
    <script src="node_modules/attendance-query.js"></script>
    
    <!-- 添加管理工具功能 -->
    <script src="node_modules/admin-tools.js"></script>
    
    <!-- 添加管理工具模态框 -->
    <div id="adminToolsModal" class="modal" style="display: none;">
        <div class="modal-content" style="width: 90%; max-width: 1200px; height: 80%; max-height: 800px; overflow: auto;">
            <span class="close-modal">&times;</span>
            <div id="admin-tools-container"></div>
        </div>
    </div>
    
    <!-- 初始化管理工具模态框 -->
    <script>
        function showAdminToolsModal() {
            // 检查用户是否为管理员（从localStorage获取当前用户）
            const currentUser = localStorage.getItem('currentUser');
            const isAdmin = currentUser === 'ling';
            
            if (!isAdmin) {
                // 如果不是管理员，显示提示信息
                alert('需要管理员权限才能访问此功能');
                return;
            }
            
            // 如果是管理员，显示管理工具界面
            const modal = document.getElementById('adminToolsModal');
            if (modal) {
                modal.style.display = 'block';
                
                // 初始化管理工具
                if (window.AdminTools) {
                    window.AdminTools.init();
                }
                
                // 绑定关闭事件
                const closeButton = modal.querySelector('.close-modal');
                if (closeButton) {
                    closeButton.onclick = function() {
                        modal.style.display = 'none';
                    };
                }
            }
        }
    </script>
</body>
</html>
